<template>
  <div class="picker">
    <Field
      @click="showPicker = true"
      :value="value"
      :label="title"
      :placeholder="placeholder"
      readonly
    />
    <Popup  v-model="showPicker" round position="bottom">
      <Picker
        :title="title"
        show-toolbar
        :columns="columns"
        @confirm="onConfirm"
        @cancel="onCancel"
      />
    </Popup >
  </div>
</template>

<script>
import { Popup , Field, Picker } from 'vant'
export default {
  components: {
    Field,
    Picker,
    Popup 
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    columns: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: ''
    },
    value: {
      type: Number | String
    }
  },
  data() {
    return {
      showPicker: false
    }
  },
  methods: {
    onConfirm(value, index) {
      this.$emit('confirm', value)
      this.showPicker = false
    },
    onCancel() {
      this.showPicker = false
    }
  }
}
</script>

<style>
</style>