<template>
  <div class="ocrStep2H5">
    <p style="color: var(--o-primary-color); padding: 0 12px">
      请确认身份信息，如无误请点击确定，如有误请手动修改
    </p>
    <div>
      <Field
        :value="value.name"
        @input="n => handleInput('name', n)"
        placeholder="请输入姓名"
        label="姓名"
      />
      <Picker
        title="性别"
        :columns="sex"
        placeholder="请选择性别"
        :value="value.sex"
        @confirm="n => handleInput('sex', n)"
      />
      <Picker
        title="民族"
        placeholder="请选择民族"
        :columns="nation"
        :value="value.nation"
        @confirm="n => handleInput('nation', n)"
      />
      <DatePick
        title="出生日期"
        placeholder="请选择日期"
        :value="value.birth"
        @confirm="n => handleInput('birth', n)"
      />
      <Field
        :value="value.address"
        @input="n => handleInput('address', n)"
        placeholder="请输入住址"
        label="住址"
      />
      <Field
        :value="value.idcardNo"
        @input="n => handleInput('idcardNo', n)"
        placeholder="请输入身份证号"
        label="身份证号"
      />
      <Field
        :value="value.authority"
        @input="n => handleInput('authority', n)"
        placeholder="请输入签发机关"
        label="签发机关"
      />
      <div style="display: flex; align-items: center">
        <DatePick
          title="有效期限"
          placeholder="请选择日期"
          :value="value.validDate1"
          @confirm="n => handleInput('validDate1', n)"
        />
        -
        <DatePick
          :value="value.validDate2"
          placeholder="请选择日期"
          @confirm="n => handleInput('validDate2', n)"
        />
      </div>
      <div style="padding: 0 16px; margin-top: 20px">
        <a
          @click="confirm"
          style="
            color: #fff;
            background: #4f71ff;
            width: 100%;
            margin-right: 10px;
            height: 44px;
            border-radius: 15px;
            line-height: 44px;
            text-align: center;
            display: block;
          "
          >确认</a
        >
      </div>
    </div>
  </div>
</template>

<script>
import { Toast, Field } from 'vant'
import Picker from './picker.vue'
import DatePick from './datePick.vue'
import store from '../../../../helpers/store'
import handleError from '../../../../helpers/handleErrorH5'
import formatDateTime from '../../../../formatters/dateTime'
import makePlatformClient from '../../../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  components: {
    Field,
    Picker,
    DatePick
  },
  props: {
    value: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      phone: '',
      sex: ['男', '女'],
      nation: [
        '汉',
        '蒙古',
        '回',
        '藏',
        '维吾尔',
        '苗',
        '彝',
        '壮',
        '布依',
        '朝鲜',
        '满',
        '侗',
        '瑶',
        '白',
        '土家',
        '哈尼',
        '哈萨克',
        '傣',
        '黎',
        '傈僳',
        '佤',
        '畲',
        '高山',
        '拉祜',
        '水',
        '东乡',
        '纳西',
        '景颇',
        '柯尔克孜',
        '土',
        '达斡尔',
        '仫佬',
        '羌',
        '布朗',
        '撒拉',
        '毛南',
        '仡佬',
        '锡伯',
        '阿昌',
        '普米',
        '塔吉克',
        '怒',
        '乌孜别克',
        '俄罗斯',
        '鄂温克',
        '德昂',
        '保安',
        '裕固',
        '京',
        '塔塔尔',
        '独龙',
        '鄂伦春',
        '赫哲',
        '门巴',
        '珞巴',
        '基诺'
      ]
    }
  },
  methods: {
    handleInput(key, value) {
      let n = { ...this.value }
      n[key] = value
      this.$emit('input', n)
    },
    async confirm() {
      if (!this.value.idcardNo) {
        handleError('身份证号不能为空')
        return
      }

      if (!this.value.sex) {
        handleError('性别不能为空')
        return
      }

      if (!this.value.nation) {
        handleError('民族不能为空')
        return
      }

      if (!this.value.name) {
        handleError('姓名不能为空')
        return
      }

      if (!this.value.birth) {
        handleError('出生日期不能为空')
        return
      }

      if (!this.value.authority) {
        handleError('签发机关不能为空')
        return
      }

      if (!this.value.address) {
        handleError('住址不能为空')
        return
      }

      if (!this.value.validDate1 || !this.value.validDate2) {
        handleError('有效期限不能为空')
        return
      }

      const birthday = formatDateTime(
        { format: 'yyyy-MM-dd' },
        this.value.birth
      )
      const idcardValidityPeriod = `${formatDateTime(
        { format: 'yyyy-MM-dd' },
        this.value.validDate1
      )}-${formatDateTime({ format: 'yyyy-MM-dd' }, this.value.validDate2)}`
      const idcardSigningOrgans = this.value.authority
      const birthAddress = this.value.address
      const body = {
        ...this.value,
        birthAddress,
        birthday,
        idcardValidityPeriod,
        idcardSigningOrgans
      }

      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        duration: 0
      })

      const [err, r] =
        await platformClient.merchantAuthenticatedProfileIdentity({
          body,
          method: 'PUT'
        })

      if (err) {
        Toast.clear()
        handleError(err)
        return
      }

      Toast.clear()
      store.set('__name__', this.value.name)
      store.set('__idCardNo__', this.value.idcardNo)

      this.$router.replace({
        path: '/livingBody'
      })
    }
  }
}
</script>

<style></style>
