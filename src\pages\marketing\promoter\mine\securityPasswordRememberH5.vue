<template>
  <div style="padding: 13px 12px 0; background: #f2f3f7">
    <Field
      style="border-radius: 8px 8px 0 0"
      v-model="oldPassword"
      type="password"
      maxlength="6"
      label="原安全密码"
      placeholder="请输入原安全密码"
    />
    <Field
      v-model="newPassword1"
      type="password"
      maxlength="6"
      label="新安全密码"
      placeholder="请输入新安全密码"
    />
    <Field
      v-model="newPassword2"
      type="password"
      maxlength="6"
      label="确认安全密码"
      placeholder="请再次输入新安全密码"
    />
    <div
      @click="$router.replace('/securityPasswordForget')"
      style="color: var(--o-primary-color); margin: 16px 0"
    >
      忘记安全密码？
    </div>
    <Button @click="updateSecurityPassword" block round type="primary"
      >确定</Button
    >
  </div>
</template>

<script>
import { <PERSON>, Button } from 'vant'
import makePlatformClient from 'kit/services/platform/makeClient'
import handleError from 'kit/helpers/handleErrorH5'
import handleSuccess from 'kit/helpers/handleSuccessH5'
const platformClient = makePlatformClient()
export default {
  components: {
    Field,
    Button
  },
  data() {
    return {
      newPassword1: '',
      newPassword2: '',
      oldPassword: '',
      passwordType: 'SAFE'
    }
  },
  methods: {
    async updateSecurityPassword() {
      if (this.newPassword1 !== this.newPassword2) {
        handleError({ message: '两次密码输入不一致' })
        return
      }

      const body = {
        newPassword: this.newPassword1,
        oldPassword: this.oldPassword,
        passwordType: 'SAFE'
      }

      const [err, r] =
        await platformClient.merchantAccountSecurityPasswordUpdate({ body })
      if (err) {
        handleError(err)
      }

      handleSuccess('更改成功')
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped></style>
