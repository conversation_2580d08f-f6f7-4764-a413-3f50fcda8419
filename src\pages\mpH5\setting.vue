<template>
  <div style="background: #f5f5f5; min-height: 100vh; padding: 20px">
    <CellGroup style="border-radius: 8px">
      <Cell @click="setEmail" size="large" title="安全邮箱" is-link />
    </CellGroup>
    <Button
      style="
        width: 100%;
        border: 1px solid #a8acba;
        background: #f5f5f5;
        border-radius: 6px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #777c94;
        letter-spacing: 0;
        text-align: center;
        line-height: 16px;
        margin-top: 200px;
      "
      @click="logout"
      type="default"
      >退出登录</Button
    >
  </div>
</template>

<script>
import { Cell, CellGroup, Button } from 'vant'
import makePlatformClient from '../../services/platform/makeClient'
import handleErrorH5 from '../../helpers/handleErrorH5'
const platformClient = makePlatformClient()

export default {
  components: {
    Button,
    Cell,
    CellGroup
  },
  data() {
    return { email: '' }
  },
  async created() {
    const [err, r] = await platformClient.merchantPlatformProfile({
      body: {}
    })

    if (err) {
      handleErrorH5(err)
      return
    }

    this.email = r.data.user.email
  },
  methods: {
    setEmail() {
      if (this.email) {
        this.$router.push({
          path: '/emailChange',
          query: { email: this.email }
        })
        return
      }

      this.$router.push('/emailNew')
    },
    logout() {
      Dialog.confirm({
        title: '提示',
        message: '确定退出吗?'
      }).then(async () => {
        const token = store.get('token')
        await platformClient.merchantPlatformLogout({
          body: {
            token
          }
        })
        this.$router.replace('/login')
      })
    }
  }
}
</script>

<style>
</style>