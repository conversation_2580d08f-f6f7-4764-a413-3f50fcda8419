import {
  ContractLogTypeStartSign,
  ContractLogTypeView,
  ContractLogTypeApprovalPassed,
  ContractLogTypeApprovalReject,
  ContractLogTypeWrite,
  ContractLogTypeSign,
  ContractLogTypeDownload,
  ContractLogTypeSendToEmail,
  ContractLogTypeWithdraw,
  ContractLogTypeUrge
} from './constants'
export default type => {
  switch (type) {
    case ContractLogTypeStartSign:
      return '发起签署'
    case ContractLogTypeView:
      return '查看'
    case ContractLogTypeApprovalPassed:
      return '审核通过'
    case ContractLogTypeApprovalReject:
      return '审核驳回'
    case ContractLogTypeWrite:
      return '提交填写'
    case ContractLogTypeSign:
      return '提交签署'
    case ContractLogTypeDownload:
      return '下载'
    case ContractLogTypeSendToEmail:
      return '发送至邮箱'
    case ContractLogTypeWithdraw:
      return '撤回'
    case ContractLogTypeUrge:
      return '催办'
    default:
      throw new Error('not supported yet')
  }
}
