<template>
  <div style="background: rgb(245, 245, 245); height: 100vh">
    <CellGroup>
      <Cell
        @click="openUserAgreement"
        size="large"
        title="用户服务协议"
        is-link
      />
      <Cell
        @click="openPrivacyAgreement"
        size="large"
        title="渤海e薪隐私协议"
        is-link
      />
    </CellGroup>
  </div>
</template>

<script>
import { Cell, CellGroup } from 'vant'

export default {
  components: {
    Cell,
    CellGroup
  },
  methods: {
    openPrivacyAgreement() {
      window.location.href = window.env.mph5URL + '/privacy.html'
    },
    openUserAgreement() {
      window.location.href = window.env.mph5URL + '/agree.html'
    }
  }
}
</script>

<style>
</style>