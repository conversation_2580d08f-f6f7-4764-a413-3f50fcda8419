<template>
  <div class="securityPasswordChange" style="padding: 0 16px">
    <Field
      v-model="oldPassword"
      type="password"
      maxlength="6"
      label="原安全密码"
      placeholder="请输入原安全密码"
    />
    <Field
      v-model="newPassword1"
      type="password"
      maxlength="6"
      label="新密码"
      placeholder="请输入新密码"
    />
    <Field
      v-model="newPassword2"
      type="password"
      maxlength="6"
      label="确认密码"
      placeholder="请输入确认密码"
    />
    <p>安全密码为6位数字</p>
    <div
      @click="$router.replace('/securityPasswordForgetChange')"
      style="color: var(--o-primary-color); margin: 16px 0"
    >
      忘记安全密码？
    </div>
    <Button @click="updateSecurityPassword" block round type="primary"
      >确定</Button
    >
  </div>
</template>

<script>
import { Field, Button } from 'vant'
import makePlatformClient from '../../services/platform/makeClient'
import handleError from '../../helpers/handleErrorH5'
import handleSuccess from '../../helpers/handleSuccessH5'
const platformClient = makePlatformClient()
export default {
  components: {
    Field,
    Button
  },
  data() {
    return {
      newPassword1: '',
      newPassword2: '',
      oldPassword: '',
      passwordType: 'SAFE'
    }
  },
  methods: {
    async updateSecurityPassword() {
      const reg = /[^0-9]/g

      if (this.oldPassword === '' || reg.test(this.oldPassword)) {
        handleError({ message: '原安全密码格式不正确' })
        return
      }

      if (this.newPassword1 === '' || reg.test(this.newPassword1)) {
        handleError({ message: '新密码格式不正确' })
        return
      }

      if (this.newPassword2 === '' || reg.test(this.newPassword2)) {
        handleError({ message: '确认密码格式不正确' })
        return
      }

      if (this.newPassword1 !== this.newPassword2) {
        handleError({ message: '两次密码输入不一致' })
        return
      }

      const body = {
        newPassword: this.newPassword1,
        oldPassword: this.oldPassword,
        passwordType: 'SAFE'
      }

      const [err, r] =
        await platformClient.merchantAccountSecurityPasswordUpdate({ body })
      if (err) {
        handleError(err)
        return
      }

      handleSuccess('更改成功')
      this.$router.go(-1)
    }
  }
}
</script>

<style>
</style>