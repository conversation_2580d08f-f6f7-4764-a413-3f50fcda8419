import {
  ContractRenewalStatusNoRenew,
  ContractRenewalStatusRenewing,
  ContractRenewalStatusRenewed
} from './constants'
export default type => {
  switch (type) {
    case ContractRenewalStatusNoRenew:
      return '未续约'
    case ContractRenewalStatusRenewing:
      return '续约中'
    case ContractRenewalStatusRenewed:
      return '已续约'
    default:
      throw new Error('not supported yet')
  }
}
