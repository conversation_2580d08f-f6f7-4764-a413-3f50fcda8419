<template>
  <div class="contractTemplatesSelector">
    <el-select
      v-model="selectedTemplateId"
      filterable
      remote
      :remote-method="remoteMethod"
      placeholder="请输入模板名称搜索"
      style="width: 100%"
      :loading="loading"
      @change="handleChange"
    >
      <el-option
        v-for="item in templates"
        :key="item.tempId"
        :label="item.tempName"
        :value="item.tempId"
      >
        <span style="float: left">{{ item.tempName }}</span>
        <span style="float: right; color: #8492a6; font-size: 13px">
          <el-tag :type="getStatusType(item.tempStatus)" size="mini">
            {{ getStatusText(item.tempStatus) }}
          </el-tag>
        </span>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import handleError from '../../../../helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'ContractTemplatesSelector',
  props: {
    value: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      loading: false,
      templates: [], // Templates from search results
      selectedTemplateId: null
    }
  },
  watch: {
    value: {
      handler(newValue) {
        this.selectedTemplateId = newValue
        if (newValue) {
          this.fetchTemplateById(newValue)
        }
      },
      immediate: true
    }
  },
  created() {
    this.fetchTemplates()
  },
  methods: {
    reset() {
      this.selectedTemplateId = null
    },
    async fetchTemplates(name = '') {
      this.loading = true
      const [err, r] = await client.getTemplateList({
        body: {
          offset: 0,
          limit: 1000,
          withTotal: false,
          withDisabled: false,
          withDeleted: false,
          filters: {
            tempName: name,
            tempStatus: 'ENABLED' // 只显示启用的模板
          }
        }
      })
      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      // Merge new results with existing ones, avoiding duplicates
      if (r.data && r.data.list) {
        for (const template of r.data.list) {
          if (!this.templates.find(item => item.tempId === template.tempId)) {
            this.templates.push(template)
          }
        }
      }
    },
    async fetchTemplateById(templateId) {
      if (!templateId) return

      // Check if template is already loaded
      if (this.templates.find(template => template.tempId === templateId)) {
        return
      }

      // Fetch template by ID - using the same API with no filters to get all templates
      // then filter by ID on client side since there's no direct get by ID API
      const [err, r] = await client.getTemplateList({
        body: {
          offset: 0,
          limit: 1000,
          withTotal: false,
          withDisabled: true,
          withDeleted: false,
          filters: {}
        }
      })

      if (err) {
        handleError(err)
        return
      }

      // Find the specific template and add it to the list
      if (r.data && r.data.list) {
        const template = r.data.list.find(item => item.tempId === templateId)
        if (template && !this.templates.find(item => item.tempId === template.tempId)) {
          this.templates.push(template)
        }
      }
    },
    remoteMethod(query) {
      this.fetchTemplates(query)
    },
    handleChange(value) {
      this.$emit('input', value)
      const template = this.templates.find(item => item.tempId === value)
      if (template) {
        this.$emit('change', template)
      }
    },
    getStatusType(status) {
      const statusMap = {
        DRAFT: 'info',
        ERROR: 'danger',
        ENABLED: 'success',
        DISABLED: 'warning'
      }
      return statusMap[status] || 'info'
    },
    getStatusText(status) {
      const statusMap = {
        DRAFT: '草稿',
        ERROR: '错误',
        ENABLED: '启用',
        DISABLED: '停用'
      }
      return statusMap[status] || status
    }
  }
}
</script>

<style scoped>
.contractTemplatesSelector {
  width: 100%;
}
</style>