<template>
  <div class="securityPasswordForgetChange">
    <VerifyIdentidy @next="next" v-if="step === 0" />
    <template v-else>
      <Field
        v-model.number="newPassword1"
        type="password"
        maxlength="6"
        label="新密码"
        placeholder="请输入原安全密码"
      />
      <Field
        v-model.number="newPassword2"
        type="password"
        maxlength="6"
        label="确认密码"
        placeholder="请输入确认密码"
      />
      <p>安全密码为6位数字</p>
      <Button @click="updateSecurityPassword" block round type="primary"
        >确定</Button
      >
    </template>
  </div>
</template>

<script>
import { Field, Button } from 'vant'
import VerifyIdentidy from './verifyIdentidy.vue'
import makePlatformClient from '../../services/platform/makeClient'
import handleError from '../../helpers/handleErrorH5'
import handleSuccess from '../../helpers/handleSuccessH5'
const platformClient = makePlatformClient()
export default {
  components: {
    Field,
    Button,
    VerifyIdentidy
  },
  data() {
    return {
      newPassword1: '',
      newPassword2: '',
      passwordType: 'SAFE',
      otp: {
        answer: '',
        token: ''
      },
      step: 0
    }
  },
  methods: {
    async updateSecurityPassword() {
      const reg = /[^0-9]/g

      if (this.newPassword1 === '' || reg.test(this.newPassword1)) {
        handleError({ message: '新密码格式不正确' })
        return
      }

      if (this.newPassword2 === '' || reg.test(this.newPassword2)) {
        handleError({ message: '确认密码格式不正确' })
        return
      }

      if (this.newPassword1 !== this.newPassword2) {
        handleError({ message: '两次密码输入不一致' })
        return
      }

      const body = {
        challenge: this.otp.answer,
        mode: 'SMS',
        otpToken: this.otp.token,
        password: this.newPassword1,
        type: 'SECURITY'
      }

      const [err, r] = await platformClient.merchantPlatformModifyPassword({
        body
      })
      if (err) {
        handleError(err)
        return
      }

      handleSuccess('更改成功')
      this.$router.go(-1)
    },
    next(otp) {
      this.step = 1
      this.otp = otp
    }
  }
}
</script>

<style>
</style>