import {
  ContractModificationTypeReissue,
  ContractModificationTypeRenewal,
  ContractModificationTypeUpdate,
  ContractModificationTypeTerminate
} from './constants'

export default type => {
  switch (type) {
    case ContractModificationTypeTerminate:
      return '解约'
    case ContractModificationTypeRenewal:
      return '续约'
    case ContractModificationTypeUpdate:
      return '变更'
    case ContractModificationTypeReissue:
      return '重新发起'
  }
}
