<template >
  <div class="emailNew" style="">
    <template>
      <Field
        v-model="email"
        placeholder="请输入新的邮箱地址"
        label="安全邮箱"
      />
      <Field
        v-model="code"
        maxlength="6"
        placeholder="请输入邮箱验证码"
        label="邮箱验证码"
      >
        <template #button>
          <Button
            class="sms-button"
            size="small"
            type="primary"
            plain
            :loading="isLoading"
            @click="send"
            v-show="!countdown"
          >
            获取验证码
          </Button>
          <div v-show="countdown">
            <Countdown
              template="%ds后重新获取"
              ref="countdown"
              @finish="countdown = false"
            />
          </div>
        </template>
      </Field>

      <div style="margin: 16px">
        <Button @click="submit" round block type="primary">确定</Button>
      </div>
    </template>
  </div>
</template>

<script>
import { Form, Field, Button } from 'vant'
import Countdown from '../../components/ui/countdown.vue'
import makePlatformClient from '../../services/platform/makeClient'
import handleError from '../../helpers/handleErrorH5'
import handleSuccess from '../../helpers/handleSuccessH5'
const platformClient = makePlatformClient()

export default {
  components: {
    Form,
    Field,
    Button,
    Countdown
  },
  data() {
    return {
      code: '',
      email: '',
      token: '',
      countdown: false,
      isLoading: false
    }
  },

  methods: {
    async send() {
      const emailReg = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

      if (!emailReg.test(this.email)) {
        handleError('请输入正确的邮箱地址')
        return
      }

      this.isLoading = true

      const [err, r] = await platformClient.merchantEmailSend({
        body:{
          email: this.email
        }
      })

      if (err) {
        this.isLoading = false
        handleError(err)
        return
      }

      this.countdown = true
      this.isLoading = false
      this.token = r.data.emailToken
    },
    async submit() {
      if(!this.token){
        handleError('请先获取验证码')
        return
      }

      if(!this.code){
        handleError('请输入邮箱验证码')
        return
      }

      const [err, r] = await platformClient.merchantEmailUpdate({
        body: {
          authCode: this.code,
          email: this.email,
          token:this.token
        }
      })

      if (err) {
        handleError(err)
        return
      }

      handleSuccess('设置成功')
      this.$router.replace('/setting')
    }
  }
}
</script>

<style>
</style>