<template>
  <div style="padding: 60px 16px">
    <div
      style="
        width: 140px;
        height: 140px;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f3f3f3;
      "
    >
      <img src="../../../../assets/images/face_auth.png" alt="" />
    </div>
    <div
      style="
        display: flex;
        justify-content: space-between;
        margin-top: 32px;
        padding: 0 15px;
      "
    >
      <div class="example">
        <img
          style="width: 32px; height: 32px; margin-bottom: 4px"
          src="../../../../assets/images/icon/icon_strong_weak_light.png"
          alt=""
        />
        <div>
          <i
            style="color: #f63b3b; margin-right: 4px"
            class="iconfont icon-remind-close"
          ></i>
          <span style="color: #828b9b">避免强弱光</span>
        </div>
      </div>
      <div class="example">
        <img
          style="width: 32px; height: 32px; margin-bottom: 4px"
          src="../../../../assets/images/icon/icon-face.png"
          alt=""
        />
        <div>
          <i
            style="color: #0bbc0a; margin-right: 4px"
            class="iconfont icon-remind-close"
          ></i>
          <span style="color: #828b9b">脸在取景框内</span>
        </div>
      </div>
      <div class="example">
        <img
          style="width: 32px; height: 32px; margin-bottom: 4px"
          src="../../../../assets/images/icon/icon-phone.png"
          alt=""
        />
        <div>
          <i
            style="color: #0bbc0a; margin-right: 4px"
            class="iconfont icon-remind-complete"
          ></i>
          <span style="color: #828b9b">需正对屏幕</span>
        </div>
      </div>
    </div>

    <a
      @click="startVerification"
      style="
        margin-top: 40px;
        color: #fff;
        background: #4f71ff;
        width: 100%;
        margin-right: 10px;
        height: 44px;
        border-radius: 15px;
        line-height: 44px;
        text-align: center;
        display: block;
      "
      >开始认证</a
    >
  </div>
</template>

<script>
export default {
  methods: {
    startVerification() {
      this.$emit('startVerification')
    }
  }
}
</script>

<style scoped>
.example {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}
</style>