
class Client {
  constructor(httpClient) {
    if (!httpClient) {
      throw new Error('httpClient is required')
    }

    this.httpClient = httpClient
  }
  
async jobUpdateContractTodoStatus(options = {}) {
  const resource = '/job/updateContractTodoStatus'
  return this.httpClient.request(resource, options)
}

async jobUpdateContractStatus(options = {}) {
  const resource = '/job/updateContractStatus'
  return this.httpClient.request(resource, options)
}

async jobTryReSigningContract(options = {}) {
  const resource = '/job/tryReSigningContract'
  return this.httpClient.request(resource, options)
}

async jobTrace(options = {}) {
  const resource = '/job/trace'
  return this.httpClient.request(resource, options)
}

async jobSigningOverdueNotify(options = {}) {
  const resource = '/job/signingOverdueNotify'
  return this.httpClient.request(resource, options)
}

async jobSigningClosingNotify(options = {}) {
  const resource = '/job/signingClosingNotify'
  return this.httpClient.request(resource, options)
}

async jobRepairContractParticipant(options = {}) {
  const resource = '/job/repairContractParticipant'
  return this.httpClient.request(resource, options)
}

async jobReSignFailedContract(options = {}) {
  const resource = '/job/reSignFailedContract'
  return this.httpClient.request(resource, options)
}

async jobCreateContractSnapshotFile(options = {}) {
  const resource = '/job/createContractSnapshotFile'
  return this.httpClient.request(resource, options)
}

async jobCancelOvertimeTask(options = {}) {
  const resource = '/job/cancelOvertimeTask'
  return this.httpClient.request(resource, options)
}

async workbenchStatis(options = {}) {
  const resource = '/api/workbench/statis'
  return this.httpClient.request(resource, options)
}

async workbenchQueryContract(options = {}) {
  const resource = '/api/workbench/queryContract'
  return this.httpClient.request(resource, options)
}

async workbenchQueryClosingSoon(options = {}) {
  const resource = '/api/workbench/queryClosingSoon'
  return this.httpClient.request(resource, options)
}

async workbenchMySigningDraft(options = {}) {
  const resource = '/api/workbench/mySigningDraft'
  return this.httpClient.request(resource, options)
}

async templateUploadSignFile(options = {}) {
  const resource = '/api/template/uploadSignFile'
  return this.httpClient.request(resource, options)
}

async templateUpdate(options = {}) {
  const resource = '/api/template/update'
  return this.httpClient.request(resource, options)
}

async templateUpdateCustomFieldName(options = {}) {
  const resource = '/api/template/updateCustomFieldName'
  return this.httpClient.request(resource, options)
}

async templateSave(options = {}) {
  const resource = '/api/template/save'
  return this.httpClient.request(resource, options)
}

async templateSaveCustomField(options = {}) {
  const resource = '/api/template/saveCustomField'
  return this.httpClient.request(resource, options)
}

async templateRemove(options = {}) {
  const resource = '/api/template/remove'
  return this.httpClient.request(resource, options)
}

async templateRemoveCustomField(options = {}) {
  const resource = '/api/template/removeCustomField'
  return this.httpClient.request(resource, options)
}

async templateQuery(options = {}) {
  const resource = '/api/template/query'
  return this.httpClient.request(resource, options)
}

async templateMakeSubmit(options = {}) {
  const resource = '/api/template/makeSubmit'
  return this.httpClient.request(resource, options)
}

async templateMakeSave(options = {}) {
  const resource = '/api/template/makeSave'
  return this.httpClient.request(resource, options)
}

async templateMakeDetail(options = {}) {
  const resource = '/api/template/makeDetail'
  return this.httpClient.request(resource, options)
}

async templateGet(options = {}) {
  const resource = '/api/template/get'
  return this.httpClient.request(resource, options)
}

async templateEnable(options = {}) {
  const resource = '/api/template/enable'
  return this.httpClient.request(resource, options)
}

async templateDisable(options = {}) {
  const resource = '/api/template/disable'
  return this.httpClient.request(resource, options)
}

async templateDisableCheck(options = {}) {
  const resource = '/api/template/disableCheck'
  return this.httpClient.request(resource, options)
}

async signingWithdraw(options = {}) {
  const resource = '/api/signing/withdraw'
  return this.httpClient.request(resource, options)
}

async signingUrge(options = {}) {
  const resource = '/api/signing/urge'
  return this.httpClient.request(resource, options)
}

async signingUpdateSignAttachment(options = {}) {
  const resource = '/api/signing/updateSignAttachment'
  return this.httpClient.request(resource, options)
}

async signingUpdateDraft(options = {}) {
  const resource = '/api/signing/updateDraft'
  return this.httpClient.request(resource, options)
}

async signingSubmitDraft(options = {}) {
  const resource = '/api/signing/submitDraft'
  return this.httpClient.request(resource, options)
}

async signingSign(options = {}) {
  const resource = '/api/signing/sign'
  return this.httpClient.request(resource, options)
}

async signingSignPreview(options = {}) {
  const resource = '/api/signing/signPreview'
  return this.httpClient.request(resource, options)
}

async signingSaveDraft(options = {}) {
  const resource = '/api/signing/saveDraft'
  return this.httpClient.request(resource, options)
}

async signingSaveDraftModifiableData(options = {}) {
  const resource = '/api/signing/saveDraftModifiableData'
  return this.httpClient.request(resource, options)
}

async signingSaveContractWriteableData(options = {}) {
  const resource = '/api/signing/saveContractWriteableData'
  return this.httpClient.request(resource, options)
}

async signingRemoveDraft(options = {}) {
  const resource = '/api/signing/removeDraft'
  return this.httpClient.request(resource, options)
}

async signingQueryUser(options = {}) {
  const resource = '/api/signing/queryUser'
  return this.httpClient.request(resource, options)
}

async signingQueryDraft(options = {}) {
  const resource = '/api/signing/queryDraft'
  return this.httpClient.request(resource, options)
}

async signingQueryContract(options = {}) {
  const resource = '/api/signing/queryContract'
  return this.httpClient.request(resource, options)
}

async signingListSignSeal(options = {}) {
  const resource = '/api/signing/listSignSeal'
  return this.httpClient.request(resource, options)
}

async signingInfo(options = {}) {
  const resource = '/api/signing/info'
  return this.httpClient.request(resource, options)
}

async signingGetDraft(options = {}) {
  const resource = '/api/signing/getDraft'
  return this.httpClient.request(resource, options)
}

async signingGetDraftModifiableData(options = {}) {
  const resource = '/api/signing/getDraftModifiableData'
  return this.httpClient.request(resource, options)
}

async signingGetContractWriteData(options = {}) {
  const resource = '/api/signing/getContractWriteData'
  return this.httpClient.request(resource, options)
}

async signingExportContract(options = {}) {
  const resource = '/api/signing/exportContract'
  return this.httpClient.request(resource, options)
}

async signingDetail(options = {}) {
  const resource = '/api/signing/detail'
  return this.httpClient.request(resource, options)
}

async signingCheckSignFile(options = {}) {
  const resource = '/api/signing/checkSignFile'
  return this.httpClient.request(resource, options)
}

async signingCheckExport(options = {}) {
  const resource = '/api/signing/checkExport'
  return this.httpClient.request(resource, options)
}

async signingApprove(options = {}) {
  const resource = '/api/signing/approve'
  return this.httpClient.request(resource, options)
}

async mobileWorkbenchStatis(options = {}) {
  const resource = '/api/mobile/workbench/statis'
  return this.httpClient.request(resource, options)
}

async mobileWorkbenchCanOperateContract(options = {}) {
  const resource = '/api/mobile/workbench/canOperateContract'
  return this.httpClient.request(resource, options)
}

async mobileSigningUpdateSignAttachment(options = {}) {
  const resource = '/api/mobile/signing/updateSignAttachment'
  return this.httpClient.request(resource, options)
}

async mobileSigningSign(options = {}) {
  const resource = '/api/mobile/signing/sign'
  return this.httpClient.request(resource, options)
}

async mobileSigningSignPreview(options = {}) {
  const resource = '/api/mobile/signing/signPreview'
  return this.httpClient.request(resource, options)
}

async mobileSigningSaveContractWriteableData(options = {}) {
  const resource = '/api/mobile/signing/saveContractWriteableData'
  return this.httpClient.request(resource, options)
}

async mobileSigningQueryContract(options = {}) {
  const resource = '/api/mobile/signing/queryContract'
  return this.httpClient.request(resource, options)
}

async mobileSigningListSignSeal(options = {}) {
  const resource = '/api/mobile/signing/listSignSeal'
  return this.httpClient.request(resource, options)
}

async mobileSigningInfo(options = {}) {
  const resource = '/api/mobile/signing/info'
  return this.httpClient.request(resource, options)
}

async mobileSigningGetContractWriteData(options = {}) {
  const resource = '/api/mobile/signing/getContractWriteData'
  return this.httpClient.request(resource, options)
}

async mobileSigningDetail(options = {}) {
  const resource = '/api/mobile/signing/detail'
  return this.httpClient.request(resource, options)
}

async mobileSigningCheckSignFile(options = {}) {
  const resource = '/api/mobile/signing/checkSignFile'
  return this.httpClient.request(resource, options)
}

async mobileSigningApprove(options = {}) {
  const resource = '/api/mobile/signing/approve'
  return this.httpClient.request(resource, options)
}

async mobileContractSendContractFileEmail(options = {}) {
  const resource = '/api/mobile/contract/sendContractFileEmail'
  return this.httpClient.request(resource, options)
}

async mobileContractQuery(options = {}) {
  const resource = '/api/mobile/contract/query'
  return this.httpClient.request(resource, options)
}

async mobileContractHistoryQuery(options = {}) {
  const resource = '/api/mobile/contract/historyQuery'
  return this.httpClient.request(resource, options)
}

async mobileContractGetContractFile(options = {}) {
  const resource = '/api/mobile/contract/getContractFile'
  return this.httpClient.request(resource, options)
}

async mobileContractDownload(options = {}) {
  const resource = '/api/mobile/contract/download'
  return this.httpClient.request(resource, options)
}

async fileUpload(options = {}) {
  const resource = '/api/file/upload'
  return this.httpClient.request(resource, options)
}

async fileInfo(options = {}) {
  const resource = '/api/file/info'
  return this.httpClient.request(resource, options)
}

async contractTypeUpdate(options = {}) {
  const resource = '/api/contract/type/update'
  return this.httpClient.request(resource, options)
}

async contractTypeUpdateTypeSort(options = {}) {
  const resource = '/api/contract/type/updateTypeSort'
  return this.httpClient.request(resource, options)
}

async contractTypeSave(options = {}) {
  const resource = '/api/contract/type/save'
  return this.httpClient.request(resource, options)
}

async contractTypeRemove(options = {}) {
  const resource = '/api/contract/type/remove'
  return this.httpClient.request(resource, options)
}

async contractTypeRemoveCheck(options = {}) {
  const resource = '/api/contract/type/removeCheck'
  return this.httpClient.request(resource, options)
}

async contractTypeQuery(options = {}) {
  const resource = '/api/contract/type/query'
  return this.httpClient.request(resource, options)
}

async contractTypeQueryById(options = {}) {
  const resource = '/api/contract/type/queryById'
  return this.httpClient.request(resource, options)
}

async contractTypeGroupUpdateSort(options = {}) {
  const resource = '/api/contract/type/group/updateSort'
  return this.httpClient.request(resource, options)
}

async contractTypeGroupRename(options = {}) {
  const resource = '/api/contract/type/group/rename'
  return this.httpClient.request(resource, options)
}

async contractTypeGroupRemove(options = {}) {
  const resource = '/api/contract/type/group/remove'
  return this.httpClient.request(resource, options)
}

async contractTypeGroupGetTypeGroupDict(options = {}) {
  const resource = '/api/contract/type/group/getTypeGroupDict'
  return this.httpClient.request(resource, options)
}

async contractTypeGroupAdd(options = {}) {
  const resource = '/api/contract/type/group/add'
  return this.httpClient.request(resource, options)
}

async contractTypeGetTypeTree(options = {}) {
  const resource = '/api/contract/type/getTypeTree'
  return this.httpClient.request(resource, options)
}

async contractTypeGetTypeDict(options = {}) {
  const resource = '/api/contract/type/getTypeDict'
  return this.httpClient.request(resource, options)
}

async contractTypeEnable(options = {}) {
  const resource = '/api/contract/type/enable'
  return this.httpClient.request(resource, options)
}

async contractTypeDisable(options = {}) {
  const resource = '/api/contract/type/disable'
  return this.httpClient.request(resource, options)
}

async contractTypeDisableCheck(options = {}) {
  const resource = '/api/contract/type/disableCheck'
  return this.httpClient.request(resource, options)
}

async contractTest(options = {}) {
  const resource = '/api/contract/test'
  return this.httpClient.request(resource, options)
}

async contractTerminate(options = {}) {
  const resource = '/api/contract/terminate'
  return this.httpClient.request(resource, options)
}

async contractQuery(options = {}) {
  const resource = '/api/contract/query'
  return this.httpClient.request(resource, options)
}

async contractNoRuleUpdate(options = {}) {
  const resource = '/api/contract/noRule/update'
  return this.httpClient.request(resource, options)
}

async contractNoRuleSave(options = {}) {
  const resource = '/api/contract/noRule/save'
  return this.httpClient.request(resource, options)
}

async contractNoRuleRemove(options = {}) {
  const resource = '/api/contract/noRule/remove'
  return this.httpClient.request(resource, options)
}

async contractNoRuleRemoveCheck(options = {}) {
  const resource = '/api/contract/noRule/removeCheck'
  return this.httpClient.request(resource, options)
}

async contractNoRuleQuery(options = {}) {
  const resource = '/api/contract/noRule/query'
  return this.httpClient.request(resource, options)
}

async contractNoRuleQueryById(options = {}) {
  const resource = '/api/contract/noRule/queryById'
  return this.httpClient.request(resource, options)
}

async contractNoRuleGetNoRuleDict(options = {}) {
  const resource = '/api/contract/noRule/getNoRuleDict'
  return this.httpClient.request(resource, options)
}

async contractNoRuleGenerateNumber(options = {}) {
  const resource = '/api/contract/noRule/generateNumber'
  return this.httpClient.request(resource, options)
}

async contractNoRuleEnable(options = {}) {
  const resource = '/api/contract/noRule/enable'
  return this.httpClient.request(resource, options)
}

async contractNoRuleDisable(options = {}) {
  const resource = '/api/contract/noRule/disable'
  return this.httpClient.request(resource, options)
}

async contractNoRuleDisableCheck(options = {}) {
  const resource = '/api/contract/noRule/disableCheck'
  return this.httpClient.request(resource, options)
}

async contractNoRuleCheckRule(options = {}) {
  const resource = '/api/contract/noRule/checkRule'
  return this.httpClient.request(resource, options)
}

async contractExport(options = {}) {
  const resource = '/api/contract/export'
  return this.httpClient.request(resource, options)
}

async contractDownload(options = {}) {
  const resource = '/api/contract/download'
  return this.httpClient.request(resource, options)
}

async contractCheckExport(options = {}) {
  const resource = '/api/contract/checkExport'
  return this.httpClient.request(resource, options)
}

async contractCheckDownloadTask(options = {}) {
  const resource = '/api/contract/checkDownloadTask'
  return this.httpClient.request(resource, options)
}

async approveUpdate(options = {}) {
  const resource = '/api/approve/update'
  return this.httpClient.request(resource, options)
}

async approveUpdateApproveSort(options = {}) {
  const resource = '/api/approve/updateApproveSort'
  return this.httpClient.request(resource, options)
}

async approveSave(options = {}) {
  const resource = '/api/approve/save'
  return this.httpClient.request(resource, options)
}

async approveRemove(options = {}) {
  const resource = '/api/approve/remove'
  return this.httpClient.request(resource, options)
}

async approveQuery(options = {}) {
  const resource = '/api/approve/query'
  return this.httpClient.request(resource, options)
}

async approveQueryById(options = {}) {
  const resource = '/api/approve/queryById'
  return this.httpClient.request(resource, options)
}

async approveGroupUpdateSort(options = {}) {
  const resource = '/api/approve/group/updateSort'
  return this.httpClient.request(resource, options)
}

async approveGroupRename(options = {}) {
  const resource = '/api/approve/group/rename'
  return this.httpClient.request(resource, options)
}

async approveGroupRemove(options = {}) {
  const resource = '/api/approve/group/remove'
  return this.httpClient.request(resource, options)
}

async approveGroupGetTypeGroupDict(options = {}) {
  const resource = '/api/approve/group/getTypeGroupDict'
  return this.httpClient.request(resource, options)
}

async approveGroupAdd(options = {}) {
  const resource = '/api/approve/group/add'
  return this.httpClient.request(resource, options)
}

async approveEnable(options = {}) {
  const resource = '/api/approve/enable'
  return this.httpClient.request(resource, options)
}

async approveDisable(options = {}) {
  const resource = '/api/approve/disable'
  return this.httpClient.request(resource, options)
}

async approveDisableCheck(options = {}) {
  const resource = '/api/approve/disableCheck'
  return this.httpClient.request(resource, options)
}

async approveCheckRelateContractType(options = {}) {
  const resource = '/api/approve/checkRelateContractType'
  return this.httpClient.request(resource, options)
}

}

export default Client
