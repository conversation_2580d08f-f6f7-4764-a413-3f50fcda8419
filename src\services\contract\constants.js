//签署方类型
export const SingerTypePerson = '1' //个人
export const SingerTypeCompany = '2' //公司

//字段类型
//1 2 3 统称为签章类型
//自定义类型与文本类型几乎一致 当前仅在概念上区分它们
export const FieldTypePerson = '1' //签章-个人
export const FieldTypeCompany = '2' //签章-公司
export const FieldTypeDate = '3' //签章-日期
export const FieldTypeText = '4' //文本
export const FieldTypeCustom = '5' //自定义

// 文本输入类型
export const WriteTypeSingleLineText = '1' //单行文本
export const WriteTypeMultiLineText = '2' //多行文本
export const WriteTypeDate = '3' //日期

// 合同状态
export const ContractStatusReviewing = '1' //审核中
export const ContractStatusFilling = '2' // 填写中
export const ContractStatusSigning = '3' //签署中
export const ContractStatusWithdrew = '4' //已撤回
export const ContractStatusOverdue = '5' // 已逾期
export const ContractStatusCompleted = '6' //已完成
export const ContractStatusRejected = '7' //已拒绝
export const ContractStatusDeadline = '-999' //即将截止

//合同 填写流程状态
export const ContractWriteProcessStatusEmpty = '0' //空
export const ContractWriteProcessStatusWaitingSend = '1' //待发送
export const ContractWriteProcessStatusWaitingWrite = '2' //待填(当前处理人）
export const ContractWriteProcessStatusWrote = '3' //已填

export const ContractSignProcessStatusEmpty = '0' //已签
export const ContractSignProcessStatusWaitingSend = '1' //待发送
export const ContractSignProcessStatusWaitingSign = '2' //待签
export const ContractSignProcessStatusSigned = '3' //已签
export const ContractSignProcessStatusFailed = '4' //签署失败

//签章状态 在文件列表中的控件列表中
export const SignStatusNoSignaure = '1' //1-未签署
export const SignStatusSigning = '2' //2-当前正在签署
export const SignStatusSigned = '3' //3-已签署；

//文件事件流 操作类型
export const ContractLogTypeStartSign = '1' //发起签署
export const ContractLogTypeView = '2' //查看
export const ContractLogTypeApprovalPassed = '3' //审核通过
export const ContractLogTypeApprovalReject = '4' //审核驳回
export const ContractLogTypeWrite = '5' //提交填写
export const ContractLogTypeSign = '6' //提交签署
export const ContractLogTypeDownload = '7' //下载
export const ContractLogTypeSendToEmail = '8' //发送至邮箱
export const ContractLogTypeWithdraw = '9' //撤回
export const ContractLogTypeUrge = '10' //催办

//合同变更类型
export const ContractModificationTypeTerminate = '1' //解约
export const ContractModificationTypeRenewal = '2' //续约
export const ContractModificationTypeUpdate = '3' //变更
export const ContractModificationTypeReissue = '4' //重新发起

//解约类型
export const ContractTerminationTypeElectron = '1' //发起电子解约协议
export const ContractTerminationTypePaper = '2' //签署纸质协议
export const ContractTerminationTypeNoContract = '3' //不签署协议

//合同续约状态
export const ContractRenewalStatusNoRenew = '1' //未续约
export const ContractRenewalStatusRenewing = '2' //续约中
export const ContractRenewalStatusRenewed = '3' //已续约

// 合同履约状态：
export const ContractProcessStatusUnsigned = '1' //未签署
export const ContractProcessStatusIneffective = '2' //未生效
export const ContractProcessStatusEffective = '3' //生效中
export const ContractProcessStatusHaveExpired = '4' //已到期
export const ContractProcessStatusCancelled = '5' //已解约

// 合同审核状态
export const ContractApprovalStatusWaitingSend = '1' //待发送
export const ContractApprovalStatusReviewing = '2' //审核中
export const ContractApprovalStatusPassed = '3' //通过
export const ContractApprovalStatusRejected = '4' //拒绝

// 合同解约状态
export const ContractStatusNoTerminate = '1' //1-未签署
export const ContractStatusTerminateing = '2' //2-当前正在签署
export const ContractStatusTerminated = '3' //3-已签署；

// 合同续签状态
export const ContractStatusNoRenewal = '1' //1-未续约
export const ContractStatusRenewaling = '2' //2-当前正在续约
export const ContractStatusRenewaled = '3' //3-已续约；

// 合同变更状态
export const ContractStatusNoChange = '1' //1-未变更
export const ContractStatusChangeing = '2' //2-当前正在变更
export const ContractStatusChangeed = '3' //3-已变更；

// 模板状态
export const TemplateStatusDraft = '1' //草稿
export const TemplateStatusOpened = '2' //已启用
export const TemplateStatusStopped = '3' //已停用

// 合同签署方式
export const ContractSignTypeNolimit = '1' // 不限制
export const ContractSignTypeSystemSignature = '2' // 系统标准签章
export const ContractSignTypeHandwriting = '3' // 手写

// 编号类型
export const rulesTypeText = '1' //文本
export const rulesTypeDate = '2' //日期
export const rulesTypeAutoIncrementNumber = '3' //自增流水号
export const rulesTypeRandomNumber = '4' //随机流水号

// 合同文件下载状态
export const constractDownloadStatusInProcess = '1' //处理中（循环调用checkDownloadTask检测任务）
export const constractDownloadStatusSuccess = '2' // 成功

// 签署截止日期类型
export const contractSignDeadLineWayInitiateAfertSet = '1' // 发起合同时设置
export const contractSignDeadLineWayInitiateFixedDays = '2' // 发起合同后固定天数
export const contractSignDeadLineWayUnlimited = '3' // 不限制
