<template>
  <el-dialog
    title="确认开票"
    :visible.sync="dialogVisible"
    width="500px"
    @close="handleClose"
  >
    <el-upload
      ref="upload"
      action="#"
      :http-request="handleUpload"
      :on-success="handleSuccess"
      :before-upload="beforeUpload"
      :file-list="fileList"
      :multiple="false"
      :limit="1"
      @on-exceed="handleExceed"
      accept=".pdf"
      :show-file-list="true"
      :auto-upload="true"
    >
      <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
      只能上传一个pdf文件，且不超过15MB
      <div slot="tip" class="el-upload__tip">
        确认开票后发票申请将无法退回，确认已开具发票吗
      </div>
    </el-upload>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleClose">确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
import handleError from 'kit/helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'
const client = makeClient()
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    invoiceId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      fileList: [],
      dialogVisible: this.visible
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (!val) {
        this.fileList = []
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    beforeUpload(file) {
      const isPDF = file.type === 'application/pdf'
      const isLt15M = file.size / 1024 / 1024 < 15

      if (!isPDF) {
        this.$message.error('上传文件只能是 PDF 格式!')
      }
      if (!isLt15M) {
        this.$message.error('上传文件大小不能超过 15MB!')
      }
      return isPDF && isLt15M
    },
    async handleUpload(options) {
      const { file } = options
      const formData = new FormData()
      formData.append('file', file)
      formData.append('invoiceId', this.invoiceId)
      const [err, r] = await client.supplierInvoicesUploadFile({
        body: formData,
        requestInterceptor(resource, options) {
          delete options.headers['Content-Type']
          return [null, resource, options]
        }
      })
      if (err) {
        handleError(err)
        return
      }

      this.handleSuccess(file)
    },
    handleSuccess(file) {
      this.$message.success(`${file.name} 上传成功`)
      this.fileList.push(file)
    },
    handleExceed() {
      this.$message.warning('只能上传一个文件')
    }
  }
}
</script>

<style>
.el-upload-list__item .el-icon-close {
  display: none !important;
}
</style>
