import formatDateTime from '../../../formatters/dateTime.js'
const stringToDate = birth => {
  // return ''
  if (!birth) {
    return ''
  }

  const reg = /([0-9]+)/g
  const tmp = [...birth.matchAll(reg)]
  if (tmp.length !== 3) {
    return ''
  }

  const t = formatDateTime(
    { format: 'yyyy/MM/dd' },
    `${tmp[0][0]}/${tmp[1][0]}/${tmp[2][0]}`
  )
  //  const t = 'invalidData-undefined-undefined'

  const reg2 = /[0-9]{4}\/[0-9]{2}\/[0-9]{2}/
  if (!reg2.test(t)) {
    return ''
  }
  return t
}

// exports.stringToDate = stringToDate
export default stringToDate
