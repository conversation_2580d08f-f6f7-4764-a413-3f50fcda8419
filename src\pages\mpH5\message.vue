<template>
  <div style="padding: 0 10px">
    <h3>{{message.title}}</h3>
    <div style="display: flex; justify-content: space-between;color:#777">
      <span>{{message.applicationSourceStr}}</span>
      <span>{{message.updateTime}}</span>
    </div>
    <p style="font-size: 14px;color:#333;border-top: 1px solid #ccc;" v-html="message.content"/>
  </div>
</template>

<script>
import handleError from '../../helpers/handleErrorH5'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  name: 'message',
  data(){
    return{
      message: {}
    }
  },
  async created(){
    const id = this.$route.params.id
    const noticeType = this.$route.params.noticeType
    const [err,r] = await platformClient.merchantMessageDetail({
      body:{
        adviseId:id,
        noticeType
      }
    })

    if(err){
      handleError(err)
      return
    }

    this.message = r.data
  }
}
</script>

<style>
</style>