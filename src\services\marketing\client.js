class Client {
  constructor(httpClient) {
    if (!httpClient) {
      throw new Error('httpClient is required')
    }

    this.httpClient = httpClient
  }

  async wechatGetOpenId(options = {}) {
    const resource = '/api/wechat/getOpenId'
    return this.httpClient.request(resource, options)
  }

  async userSmsLogin(options = {}) {
    const resource = '/api/user/smsLogin'
    return this.httpClient.request(resource, options)
  }

  async userLogout(options = {}) {
    const resource = '/api/user/logout'
    return this.httpClient.request(resource, options)
  }

  async userChangeMerchant(options = {}) {
    const resource = '/api/user/changeMerchant'
    return this.httpClient.request(resource, options)
  }

  async transferWxUpdate(options = {}) {
    const resource = '/api/transfer/wx/update'
    return this.httpClient.request(resource, options)
  }

  async transferWxSave(options = {}) {
    const resource = '/api/transfer/wx/save'
    return this.httpClient.request(resource, options)
  }

  async transferWxQuery(options = {}) {
    const resource = '/api/transfer/wx/query'
    return this.httpClient.request(resource, options)
  }

  async transferWxQueryAwardRecord(options = {}) {
    const resource = '/api/transfer/wx/queryAwardRecord'
    return this.httpClient.request(resource, options)
  }

  async transferWxExportAwardRecord(options = {}) {
    const resource = '/api/transfer/wx/exportAwardRecord'
    return this.httpClient.request(resource, options)
  }

  async transferWxDetail(options = {}) {
    const resource = '/api/transfer/wx/detail'
    return this.httpClient.request(resource, options)
  }

  async transferWxDelete(options = {}) {
    const resource = '/api/transfer/wx/delete'
    return this.httpClient.request(resource, options)
  }

  async smsSendSmsCode(options = {}) {
    const resource = '/api/sms/sendSmsCode'
    return this.httpClient.request(resource, options)
  }

  async smsCreateImageCaptcha(options = {}) {
    const resource = '/api/sms/createImageCaptcha'
    return this.httpClient.request(resource, options)
  }

  async redeemcodeGoodsUploadCode(options = {}) {
    const resource = '/api/redeemcode/goods/uploadCode'
    return this.httpClient.request(resource, options)
  }

  async redeemcodeGoodsUpdate(options = {}) {
    const resource = '/api/redeemcode/goods/update'
    return this.httpClient.request(resource, options)
  }

  async redeemcodeGoodsSave(options = {}) {
    const resource = '/api/redeemcode/goods/save'
    return this.httpClient.request(resource, options)
  }

  async redeemcodeGoodsQuery(options = {}) {
    const resource = '/api/redeemcode/goods/query'
    return this.httpClient.request(resource, options)
  }

  async redeemcodeGoodsQueryCode(options = {}) {
    const resource = '/api/redeemcode/goods/queryCode'
    return this.httpClient.request(resource, options)
  }

  async redeemcodeGoodsExportCode(options = {}) {
    const resource = '/api/redeemcode/goods/exportCode'
    return this.httpClient.request(resource, options)
  }

  async redeemcodeGoodsDetail(options = {}) {
    const resource = '/api/redeemcode/goods/detail'
    return this.httpClient.request(resource, options)
  }

  async redeemcodeGoodsDelete(options = {}) {
    const resource = '/api/redeemcode/goods/delete'
    return this.httpClient.request(resource, options)
  }

  async redeemcodeGoodsDeleteCode(options = {}) {
    const resource = '/api/redeemcode/goods/deleteCode'
    return this.httpClient.request(resource, options)
  }

  async mobilePromoterVerifyCode(options = {}) {
    const resource = '/api/mobile/promoter/verifyCode'
    return this.httpClient.request(resource, options)
  }

  async mobilePromoterSmsLogin(options = {}) {
    const resource = '/api/mobile/promoter/smsLogin'
    return this.httpClient.request(resource, options)
  }

  async mobilePromoterChangeMobile(options = {}) {
    const resource = '/api/mobile/promoter/changeMobile'
    return this.httpClient.request(resource, options)
  }

  async mobilePromoterChangeMerchant(options = {}) {
    const resource = '/api/mobile/promoter/changeMerchant'
    return this.httpClient.request(resource, options)
  }

  async mobilePromoterActivityQueryMyCouponsActivity(options = {}) {
    const resource = '/api/mobile/promoter/activity/queryMyCouponsActivity'
    return this.httpClient.request(resource, options)
  }

  async mobilePromoterActivityMyActivityGetList(options = {}) {
    const resource = '/api/mobile/promoter/activity/myActivityGetList'
    return this.httpClient.request(resource, options)
  }

  async mobilePromoterActivityMyActivityCode(options = {}) {
    const resource = '/api/mobile/promoter/activity/myActivityCode'
    return this.httpClient.request(resource, options)
  }

  async mobilePromoterActivityInfo(options = {}) {
    const resource = '/api/mobile/promoter/activity/info'
    return this.httpClient.request(resource, options)
  }

  async mobileActivityRegister(options = {}) {
    const resource = '/api/mobile/activity/register'
    return this.httpClient.request(resource, options)
  }

  async mobileActivityInfo(options = {}) {
    const resource = '/api/mobile/activity/info'
    return this.httpClient.request(resource, options)
  }

  async mobileActivityGetAward(options = {}) {
    const resource = '/api/mobile/activity/getAward'
    return this.httpClient.request(resource, options)
  }

  async mobileActivityAwardDetail(options = {}) {
    const resource = '/api/mobile/activity/awardDetail'
    return this.httpClient.request(resource, options)
  }

  async merchantUpdateProfile(options = {}) {
    const resource = '/api/merchant/updateProfile'
    return this.httpClient.request(resource, options)
  }

  async merchantUpdateOrg(options = {}) {
    const resource = '/api/merchant/updateOrg'
    return this.httpClient.request(resource, options)
  }

  async merchantUpdateMember(options = {}) {
    const resource = '/api/merchant/updateMember'
    return this.httpClient.request(resource, options)
  }

  async merchantSetPromoter(options = {}) {
    const resource = '/api/merchant/setPromoter'
    return this.httpClient.request(resource, options)
  }

  async merchantSaveOrg(options = {}) {
    const resource = '/api/merchant/saveOrg'
    return this.httpClient.request(resource, options)
  }

  async merchantSaveMember(options = {}) {
    const resource = '/api/merchant/saveMember'
    return this.httpClient.request(resource, options)
  }

  async merchantRegister(options = {}) {
    const resource = '/api/merchant/register'
    return this.httpClient.request(resource, options)
  }

  async merchantQueryRoleUser(options = {}) {
    const resource = '/api/merchant/queryRoleUser'
    return this.httpClient.request(resource, options)
  }

  async merchantQueryOrgMember(options = {}) {
    const resource = '/api/merchant/queryOrgMember'
    return this.httpClient.request(resource, options)
  }

  async merchantQueryOrgAndMember(options = {}) {
    const resource = '/api/merchant/queryOrgAndMember'
    return this.httpClient.request(resource, options)
  }

  async merchantProfile(options = {}) {
    const resource = '/api/merchant/profile'
    return this.httpClient.request(resource, options)
  }

  async merchantOrgTree(options = {}) {
    const resource = '/api/merchant/orgTree'
    return this.httpClient.request(resource, options)
  }

  async merchantListRole(options = {}) {
    const resource = '/api/merchant/listRole'
    return this.httpClient.request(resource, options)
  }

  async merchantListOrg(options = {}) {
    const resource = '/api/merchant/listOrg'
    return this.httpClient.request(resource, options)
  }

  async merchantImportUser(options = {}) {
    const resource = '/api/merchant/importUser'
    return this.httpClient.request(resource, options)
  }

  async merchantImportOrg(options = {}) {
    const resource = '/api/merchant/importOrg'
    return this.httpClient.request(resource, options)
  }

  async merchantGetMember(options = {}) {
    const resource = '/api/merchant/getMember'
    return this.httpClient.request(resource, options)
  }

  async merchantEnableMember(options = {}) {
    const resource = '/api/merchant/enableMember'
    return this.httpClient.request(resource, options)
  }

  async merchantDisableMember(options = {}) {
    const resource = '/api/merchant/disableMember'
    return this.httpClient.request(resource, options)
  }

  async merchantDeleteOrg(options = {}) {
    const resource = '/api/merchant/deleteOrg'
    return this.httpClient.request(resource, options)
  }

  async merchantDeleteMember(options = {}) {
    const resource = '/api/merchant/deleteMember'
    return this.httpClient.request(resource, options)
  }

  async merchantChangeAdmin(options = {}) {
    const resource = '/api/merchant/changeAdmin'
    return this.httpClient.request(resource, options)
  }

  async fileUpload(options = {}) {
    const resource = '/api/file/upload'
    return this.httpClient.request(resource, options)
  }

  async fileDownloadTemplate(options = {}) {
    const resource = '/api/file/downloadTemplate'
    return this.httpClient.request(resource, options)
  }

  async couponsWxUpdateCoupons(options = {}) {
    const resource = '/api/coupons/wx/updateCoupons'
    return this.httpClient.request(resource, options)
  }

  async couponsWxSaveCoupons(options = {}) {
    const resource = '/api/coupons/wx/saveCoupons'
    return this.httpClient.request(resource, options)
  }

  async couponsWxQueryCoupons(options = {}) {
    const resource = '/api/coupons/wx/queryCoupons'
    return this.httpClient.request(resource, options)
  }

  async couponsWxQueryCouponsAwardRecord(options = {}) {
    const resource = '/api/coupons/wx/queryCouponsAwardRecord'
    return this.httpClient.request(resource, options)
  }

  async couponsWxListActivity(options = {}) {
    const resource = '/api/coupons/wx/listActivity'
    return this.httpClient.request(resource, options)
  }

  async couponsWxGetCoupons(options = {}) {
    const resource = '/api/coupons/wx/getCoupons'
    return this.httpClient.request(resource, options)
  }

  async couponsWxExportCouponsAwardRecord(options = {}) {
    const resource = '/api/coupons/wx/exportCouponsAwardRecord'
    return this.httpClient.request(resource, options)
  }

  async couponsWxDeleteCoupons(options = {}) {
    const resource = '/api/coupons/wx/deleteCoupons'
    return this.httpClient.request(resource, options)
  }

  async couponsMtUpdateCoupons(options = {}) {
    const resource = '/api/coupons/mt/updateCoupons'
    return this.httpClient.request(resource, options)
  }

  async couponsMtSaveCoupons(options = {}) {
    const resource = '/api/coupons/mt/saveCoupons'
    return this.httpClient.request(resource, options)
  }

  async couponsMtQueryCoupons(options = {}) {
    const resource = '/api/coupons/mt/queryCoupons'
    return this.httpClient.request(resource, options)
  }

  async couponsMtQueryCouponsAwardRecord(options = {}) {
    const resource = '/api/coupons/mt/queryCouponsAwardRecord'
    return this.httpClient.request(resource, options)
  }

  async couponsMtGetCoupons(options = {}) {
    const resource = '/api/coupons/mt/getCoupons'
    return this.httpClient.request(resource, options)
  }

  async couponsMtExportCouponsAwardRecord(options = {}) {
    const resource = '/api/coupons/mt/exportCouponsAwardRecord'
    return this.httpClient.request(resource, options)
  }

  async couponsMtDeleteCoupons(options = {}) {
    const resource = '/api/coupons/mt/deleteCoupons'
    return this.httpClient.request(resource, options)
  }

  async couponsDemoMeituanSend(options = {}) {
    const resource = '/api/coupons/demo/meituan/send'
    return this.httpClient.request(resource, options)
  }

  async adminUserUpdate(options = {}) {
    const resource = '/api/admin/user/update'
    return this.httpClient.request(resource, options)
  }

  async adminUserSmsLogin(options = {}) {
    const resource = '/api/admin/user/smsLogin'
    return this.httpClient.request(resource, options)
  }

  async adminUserProfile(options = {}) {
    const resource = '/api/admin/user/profile'
    return this.httpClient.request(resource, options)
  }

  async adminUserLogout(options = {}) {
    const resource = '/api/admin/user/logout'
    return this.httpClient.request(resource, options)
  }

  async adminUserList(options = {}) {
    const resource = '/api/admin/user/list'
    return this.httpClient.request(resource, options)
  }

  async adminUserDetail(options = {}) {
    const resource = '/api/admin/user/detail'
    return this.httpClient.request(resource, options)
  }

  async adminUserDelete(options = {}) {
    const resource = '/api/admin/user/delete'
    return this.httpClient.request(resource, options)
  }

  async adminUserAdd(options = {}) {
    const resource = '/api/admin/user/add'
    return this.httpClient.request(resource, options)
  }

  async adminTransactionList(options = {}) {
    const resource = '/api/admin/transaction/list'
    return this.httpClient.request(resource, options)
  }

  async adminTransactionExport(options = {}) {
    const resource = '/api/admin/transaction/export'
    return this.httpClient.request(resource, options)
  }

  async adminRoleList(options = {}) {
    const resource = '/api/admin/role/list'
    return this.httpClient.request(resource, options)
  }

  async adminReceiptPackageDownload(options = {}) {
    const resource = '/api/admin/receipt/packageDownload'
    return this.httpClient.request(resource, options)
  }

  async adminReceiptList(options = {}) {
    const resource = '/api/admin/receipt/list'
    return this.httpClient.request(resource, options)
  }

  async adminReceiptDownloadRecord(options = {}) {
    const resource = '/api/admin/receipt/downloadRecord'
    return this.httpClient.request(resource, options)
  }

  async adminReceiptDetail(options = {}) {
    const resource = '/api/admin/receipt/detail'
    return this.httpClient.request(resource, options)
  }

  async adminReceiptDeleteDownloadRecord(options = {}) {
    const resource = '/api/admin/receipt/deleteDownloadRecord'
    return this.httpClient.request(resource, options)
  }

  async adminMerchantList(options = {}) {
    const resource = '/api/admin/merchant/list'
    return this.httpClient.request(resource, options)
  }

  async adminMerchantDetail(options = {}) {
    const resource = '/api/admin/merchant/detail'
    return this.httpClient.request(resource, options)
  }

  async adminAccountStatistics(options = {}) {
    const resource = '/api/admin/account/statistics'
    return this.httpClient.request(resource, options)
  }

  async adminAccountRefund(options = {}) {
    const resource = '/api/admin/account/refund'
    return this.httpClient.request(resource, options)
  }

  async adminAccountRecharge(options = {}) {
    const resource = '/api/admin/account/recharge'
    return this.httpClient.request(resource, options)
  }

  async adminAccountList(options = {}) {
    const resource = '/api/admin/account/list'
    return this.httpClient.request(resource, options)
  }

  async adminAccountDetail(options = {}) {
    const resource = '/api/admin/account/detail'
    return this.httpClient.request(resource, options)
  }

  async adminAccountBillList(options = {}) {
    const resource = '/api/admin/account/billList'
    return this.httpClient.request(resource, options)
  }

  async adminAccountAdjustCredit(options = {}) {
    const resource = '/api/admin/account/adjustCredit'
    return this.httpClient.request(resource, options)
  }

  async activityUpdateActivity(options = {}) {
    const resource = '/api/activity/updateActivity'
    return this.httpClient.request(resource, options)
  }

  async activityTest(options = {}) {
    const resource = '/api/activity/test'
    return this.httpClient.request(resource, options)
  }

  async activityTestRandom(options = {}) {
    const resource = '/api/activity/testRandom'
    return this.httpClient.request(resource, options)
  }

  async activitySaveActivity(options = {}) {
    const resource = '/api/activity/saveActivity'
    return this.httpClient.request(resource, options)
  }

  async activityQueryActivity(options = {}) {
    const resource = '/api/activity/queryActivity'
    return this.httpClient.request(resource, options)
  }

  async activityQueryActivityAwardRecord(options = {}) {
    const resource = '/api/activity/queryActivityAwardRecord'
    return this.httpClient.request(resource, options)
  }

  async activityExportActivityAwardRecord(options = {}) {
    const resource = '/api/activity/exportActivityAwardRecord'
    return this.httpClient.request(resource, options)
  }

  async activityEnableActivity(options = {}) {
    const resource = '/api/activity/enableActivity'
    return this.httpClient.request(resource, options)
  }

  async activityDisableActivity(options = {}) {
    const resource = '/api/activity/disableActivity'
    return this.httpClient.request(resource, options)
  }

  async activityDetail(options = {}) {
    const resource = '/api/activity/detail'
    return this.httpClient.request(resource, options)
  }

  async activityDeleteActivity(options = {}) {
    const resource = '/api/activity/deleteActivity'
    return this.httpClient.request(resource, options)
  }

  async activityCheckCoupons(options = {}) {
    const resource = '/api/activity/checkCoupons'
    return this.httpClient.request(resource, options)
  }

  async accountSummary(options = {}) {
    const resource = '/api/account/summary'
    return this.httpClient.request(resource, options)
  }

  async accountQuery(options = {}) {
    const resource = '/api/account/query'
    return this.httpClient.request(resource, options)
  }

  async jobUpdateCouponsStatus(options = {}) {
    const resource = '/job/updateCouponsStatus'
    return this.httpClient.request(resource, options)
  }

  async jobUpdateActivityStatus(options = {}) {
    const resource = '/job/updateActivityStatus'
    return this.httpClient.request(resource, options)
  }

  async jobSendCouponsAward(options = {}) {
    const resource = '/job/sendCouponsAward'
    return this.httpClient.request(resource, options)
  }

  async jobManualSendCouponsAward(options = {}) {
    const resource = '/job/manualSendCouponsAward'
    return this.httpClient.request(resource, options)
  }

  async jobAwardWaitList(options = {}) {
    const resource = '/job/awardWaitList'
    return this.httpClient.request(resource, options)
  }

  async userProfile(options = {}) {
    const resource = '/api/user/profile'
    return this.httpClient.request(resource, options)
  }

  async smsImageCaptcha(options = {}) {
    const resource = '/api/sms/imageCaptcha'
    return this.httpClient.request(resource, options)
  }

  async mobilePromoterProfile(options = {}) {
    const resource = '/api/mobile/promoter/profile'
    return this.httpClient.request(resource, options)
  }

  async dictBankList(options = {}) {
    const resource = '/api/dict/bankList'
    return this.httpClient.request(resource, options)
  }

  async activityPromoterSelect(options = {}) {
    const resource = '/api/activity/promoterSelect'
    return this.httpClient.request(resource, options)
  }

  async activityCouponsSelect(options = {}) {
    const resource = '/api/activity/couponsSelect'
    return this.httpClient.request(resource, options)
  }

  async adminTransferSupplierUpdate(options = {}) {
    const resource = '/api/admin/transferSupplier/update'
    return this.httpClient.request(resource, options)
  }
  
  async adminTransferSupplierUpdateStatus(options = {}) {
    const resource = '/api/admin/transferSupplier/updateStatus'
    return this.httpClient.request(resource, options)
  }
  
  async adminTransferSupplierUpdateRoute(options = {}) {
    const resource = '/api/admin/transferSupplier/updateRoute'
    return this.httpClient.request(resource, options)
  }
  
  async adminTransferSupplierSetDefaultRoute(options = {}) {
    const resource = '/api/admin/transferSupplier/setDefaultRoute'
    return this.httpClient.request(resource, options)
  }
  
  async adminTransferSupplierPage(options = {}) {
    const resource = '/api/admin/transferSupplier/page'
    return this.httpClient.request(resource, options)
  }
  
  async adminTransferSupplierList(options = {}) {
    const resource = '/api/admin/transferSupplier/transferSupplierList'
    return this.httpClient.request(resource, options)
  }

  async adminTransferSupplierMerchantList(options = {}) {
    const resource = '/api/admin/transferSupplier/merchantList'
    return this.httpClient.request(resource, options)
  }
  
  async adminTransferSupplierFindRouteList(options = {}) {
    const resource = '/api/admin/transferSupplier/findRouteList'
    return this.httpClient.request(resource, options)
  }

  async adminTransferSupplierQueryDefaultRoute(options = {}) {
    const resource = '/api/admin/transferSupplier/queryDefaultRoute'
    return this.httpClient.request(resource, options)
  }
  
  async adminTransferSupplierDetail(options = {}) {
    const resource = '/api/admin/transferSupplier/detail'
    return this.httpClient.request(resource, options)
  }
  
  async adminTransferSupplierAdd(options = {}) {
    const resource = '/api/admin/transferSupplier/add'
    return this.httpClient.request(resource, options)
  }
  
  async adminTransferSupplierAddRoute(options = {}) {
    const resource = '/api/admin/transferSupplier/addRoute'
    return this.httpClient.request(resource, options)
  }
}

export default Client
