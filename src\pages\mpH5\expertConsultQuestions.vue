<template>
  <div style="padding: 0 20px">
    <template v-for="question in questions">
      <QuestionItem
        :key="question.consultQuestionId"
        :question="question"
        @detail="detail"
      />
    </template>
    <div v-show="!questions || !questions.length">暂无数据</div>
  </div>
</template>

<script>
import { Toast } from 'vant'
import QuestionItem from '../../components/mpH5/expertConsult/questionItem.vue'
import handleError from '../../helpers/handleErrorH5'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  components: {
    QuestionItem
  },
  data() {
    return {
      questions: []
    }
  },
  async created() {
    Toast.loading({
      message: '加载中...',
      forbidClick: true,
      loadingType: 'spinner'
    })

    const [err, r] = await platformClient.queryExpertConsultQuestions({
      body: {}
    })

    if (err) {
      handleError(err)
      return
    }

    Toast.clear()

    this.questions = r.data.list
  },
  methods: {
    detail(id) {
      this.$router.push({ path: `/expertConsultQuestion/${id}` })
    }
  }
}
</script>

<style>
</style>