class Client {
  constructor(httpClient) {
    if (!httpClient) {
      throw new Error('httpClient is required')
    }

    this.httpClient = httpClient
  }

  async hroApprovalProcessList(options = {}) {
    const resource = '/api/hro/approval/process/list'
    return this.httpClient.request(resource, options)
  }

  async hroBillList(options = {}) {
    const resource = '/api/hro/bill/list'
    return this.httpClient.request(resource, options)
  }

  async hroBillAdd(options = {}) {
    const resource = '/api/hro/bill/add'
    return this.httpClient.request(resource, options)
  }

  async hroBillConfirm(options = {}) {
    const resource = '/api/hro/bill/confirm'
    return this.httpClient.request(resource, options)
  }

  async hroBillItemUpdate(options = {}) {
    const resource = '/api/hro/bill/item/update'
    return this.httpClient.request(resource, options)
  }

  async hroBillOffset(options = {}) {
    const resource = '/api/hro/bill/offset'
    return this.httpClient.request(resource, options)
  }

  async hroBillRollback(options = {}) {
    const resource = '/api/hro/bill/rollback'
    return this.httpClient.request(resource, options)
  }

  async hroBillSendEmail(options = {}) {
    const resource = '/api/hro/bill/sendEmail'
    return this.httpClient.request(resource, options)
  }

  async hroBillSubmit(options = {}) {
    const resource = '/api/hro/bill/submit'
    return this.httpClient.request(resource, options)
  }

  async hroBillDetail(options = {}) {
    const resource = '/api/hro/bill/detail'
    return this.httpClient.request(resource, options)
  }

  async hroBillItemList(options = {}) {
    const resource = '/api/hro/bill/item/list'
    return this.httpClient.request(resource, options)
  }

  async hroBillBillItemDetail(options = {}) {
    const resource = '/api/hro/bill/billItemDetail'
    return this.httpClient.request(resource, options)
  }

  async hroBillEmployeeOtherFeeList(options = {}) {
    const resource = '/api/hro/bill/employeeOtherFeeList'
    return this.httpClient.request(resource, options)
  }

  async hroBillItemExport(options = {}) {
    const resource = '/api/hro/bill/itemExport'
    return this.httpClient.request(resource, options)
  }

  async hroCandidateAbandon(options = {}) {
    const resource = '/api/hro/candidate/abandon'
    return this.httpClient.request(resource, options)
  }

  async hroCandidateWaitEntry(options = {}) {
    const resource = '/api/hro/candidate/waitEntry'
    return this.httpClient.request(resource, options)
  }

  async hroMerchantMerchantGetMerchant(options = {}) {
    const resource = '/api/hro/merchant/merchant/getMerchant'
    return this.httpClient.request(resource, options)
  }

  async hroIncrementInfo(options = {}) {
    const resource = '/api/hro/increment/info'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerAdd(options = {}) {
    const resource = '/api/hro/customer/add'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerBasicDetail(options = {}) {
    const resource = '/api/hro/customer/basic/detail'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerContractAdd(options = {}) {
    const resource = '/api/hro/customer/contract/add'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerContractUpdate(options = {}) {
    const resource = '/api/hro/customer/contract/update'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerContractDetail(options = {}) {
    const resource = '/api/hro/customer/contract/detail'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerContractPage(options = {}) {
    const resource = '/api/hro/customer/contract/page'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerContractList(options = {}) {
    const resource = '/api/hro/customer/contract/list'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerContractManagerUpdate(options = {}) {
    const resource = '/api/hro/customer/contract/manager/update'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerContractStop(options = {}) {
    const resource = '/api/hro/customer/contract/stop'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerCorporationList(options = {}) {
    const resource = '/api/hro/customer/corporation/list'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerList(options = {}) {
    const resource = '/api/hro/customer/list'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerStatusUpdate(options = {}) {
    const resource = '/api/hro/customer/status/update'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerUpdate(options = {}) {
    const resource = '/api/hro/customer/update'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerCheckName(options = {}) {
    const resource = '/api/hro/customer/checkName'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerCorporationCheckCode(options = {}) {
    const resource = '/api/hro/customer/corporation/checkCode'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerCorporationCheckName(options = {}) {
    const resource = '/api/hro/customer/corporation/checkName'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerContractCheckName(options = {}) {
    const resource = '/api/hro/customer/contract/checkName'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerContractRenew(options = {}) {
    const resource = '/api/hro/customer/contract/renew'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerContractAccountList(options = {}) {
    const resource = '/api/hro/customer/contract/account/list'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerContractAccountItems(options = {}) {
    const resource = '/api/hro/customer/contract/account/items'
    return this.httpClient.request(resource, options)
  }

  async hroInvoiceAdd(options = {}) {
    const resource = '/api/hro/invoice/add'
    return this.httpClient.request(resource, options)
  }

  async hroInvoiceConfirm(options = {}) {
    const resource = '/api/hro/invoice/confirm'
    return this.httpClient.request(resource, options)
  }

  async hroInvoiceDetail(options = {}) {
    const resource = '/api/hro/invoice/detail'
    return this.httpClient.request(resource, options)
  }

  async hroInvoiceInvalid(options = {}) {
    const resource = '/api/hro/invoice/invalid'
    return this.httpClient.request(resource, options)
  }

  async hroInvoicePage(options = {}) {
    const resource = '/api/hro/invoice/page'
    return this.httpClient.request(resource, options)
  }

  async hroInvoiceMail(options = {}) {
    const resource = '/api/hro/invoice/mail'
    return this.httpClient.request(resource, options)
  }

  async hroInvoiceSend(options = {}) {
    const resource = '/api/hro/invoice/send'
    return this.httpClient.request(resource, options)
  }

  async hroInvoiceUpdate(options = {}) {
    const resource = '/api/hro/invoice/update'
    return this.httpClient.request(resource, options)
  }

  async hroInvoicePage2(options = {}) {
    const resource = '/api/hro/invoice/page2'
    return this.httpClient.request(resource, options)
  }

  async hroInvoiceMake(options = {}) {
    const resource = '/api/hro/invoice/make'
    return this.httpClient.request(resource, options)
  }

  async hroInvoiceBack(options = {}) {
    const resource = '/api/hro/invoice/back'
    return this.httpClient.request(resource, options)
  }

  async hroInvoiceReceived(options = {}) {
    const resource = '/api/hro/invoice/received'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerCorporationPage(options = {}) {
    const resource = '/api/hro/customer/corporation/page'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierCorporationPage(options = {}) {
    const resource = '/api/hro/supplier/corporation/page'
    return this.httpClient.request(resource, options)
  }

  async hroCustomerOptions(options = {}) {
    const resource = '/api/hro/customer/options'
    return this.httpClient.request(resource, options)
  }

  async hroAccountsetList(options = {}) {
    const resource = '/api/hro/accountset/list'
    return this.httpClient.request(resource, options)
  }

  async hroPaymentList(options = {}) {
    const resource = '/api/hro/payment/list'
    return this.httpClient.request(resource, options)
  }

  async hroPaymentOffsetList(options = {}) {
    const resource = '/api/hro/payment/offset/list'
    return this.httpClient.request(resource, options)
  }

  async hroPaymentDetail(options = {}) {
    const resource = '/api/hro/payment/detail'
    return this.httpClient.request(resource, options)
  }

  async hroPaymentExportTemplate(options = {}) {
    const resource = '/api/hro/payment/exportTemplate'
    return this.httpClient.request(resource, options)
  }

  async hroPaymentImportPayment(options = {}) {
    const resource = '/api/hro/payment/importPayment'
    return this.httpClient.request(resource, options)
  }

  async hroPaymentOffset(options = {}) {
    const resource = '/api/hro/payment/offset'
    return this.httpClient.request(resource, options)
  }

  async hroAccountsetPage(options = {}) {
    const resource = '/api/hro/accountset/page'
    return this.httpClient.request(resource, options)
  }

  async hroAccountsetUpdate(options = {}) {
    const resource = '/api/hro/accountset/update'
    return this.httpClient.request(resource, options)
  }

  async hroAccountsetEnable(options = {}) {
    const resource = '/api/hro/accountset/enable'
    return this.httpClient.request(resource, options)
  }

  async hroAccountsetDisable(options = {}) {
    const resource = '/api/hro/accountset/disable'
    return this.httpClient.request(resource, options)
  }

  async hroAccountsetAdd(options = {}) {
    const resource = '/api/hro/accountset/add'
    return this.httpClient.request(resource, options)
  }

  async hroAccountsetDetail(options = {}) {
    const resource = '/api/hro/accountset/detail'
    return this.httpClient.request(resource, options)
  }

  async hroAccountsetCheckName(options = {}) {
    const resource = '/api/hro/accountset/checkName'
    return this.httpClient.request(resource, options)
  }

  async hroContentAdd(options = {}) {
    const resource = '/api/hro/content/add'
    return this.httpClient.request(resource, options)
  }

  async hroAccountsetItemBillList(options = {}) {
    const resource = '/api/hro/accountset/item/bill/list'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierList(options = {}) {
    const resource = '/api/hro/supplier/list'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierAdd(options = {}) {
    const resource = '/api/hro/supplier/add'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierBasicDetail(options = {}) {
    const resource = '/api/hro/supplier/basic/detail'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierUpdate(options = {}) {
    const resource = '/api/hro/supplier/update'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierContractAdd(options = {}) {
    const resource = '/api/hro/supplier/contract/add'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierContractDetail(options = {}) {
    const resource = '/api/hro/supplier/contract/detail'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierCorporationList(options = {}) {
    const resource = '/api/hro/supplier/corporation/list'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierContractList(options = {}) {
    const resource = '/api/hro/supplier/contract/list'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierContractStop(options = {}) {
    const resource = '/api/hro/supplier/contract/stop'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierContractUpdate(options = {}) {
    const resource = '/api/hro/supplier/contract/update'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierDisable(options = {}) {
    const resource = '/api/hro/supplier/disable'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierEnable(options = {}) {
    const resource = '/api/hro/supplier/enable'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierContractManagerUpdate(options = {}) {
    const resource = '/api/hro/supplier/contract/manager/update'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierContractRenew(options = {}) {
    const resource = '/api/hro/supplier/contract/renew'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierCorporationCheckCode(options = {}) {
    const resource = '/api/hro/supplier/corporation/checkCode'
    return this.httpClient.request(resource, options)
  }

  async hroSupplierCheckName(options = {}) {
    const resource = '/api/hro/supplier/checkName'
    return this.httpClient.request(resource, options)
  }
}

export default Client
