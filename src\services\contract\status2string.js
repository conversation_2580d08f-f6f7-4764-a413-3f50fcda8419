import {
  ContractStatusReviewing,
  ContractStatusFilling,
  ContractStatusSigning,
  ContractStatusWithdrew,
  ContractStatusOverdue,
  ContractStatusCompleted,
  ContractStatusRejected,
  ContractStatusDeadline
} from './constants'
export default status => {
  const m = [
    { status: ContractStatusReviewing, name: '审核中' },
    { status: ContractStatusFilling, name: '填写中' },
    { status: ContractStatusSigning, name: '签署中' },
    { status: ContractStatusWithdrew, name: '已撤回' },
    { status: ContractStatusOverdue, name: '已逾期' },
    { status: ContractStatusCompleted, name: '已完成' },
    { status: ContractStatusRejected, name: '已拒绝' },
    { status: ContractStatusDeadline, name: '即将截止' }
  ]
  const c = m.find(item => item.status === status)
  if (c) {
    return c.name
  }
  return ''
}
