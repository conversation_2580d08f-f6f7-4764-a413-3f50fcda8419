<template>
  <div
    class="corporations"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 20px;
        background: var(--o-primary-bg-color);
        padding: 20px;
        border-radius: 5px;
        display: flex;
        justify-content: space-between;
      "
      label-position="right"
      label-width="110px"
    >
      <el-form-item label="作业主体名称" style="margin-bottom: 0">
        <el-input
          v-model="conditions.filters.name"
          placeholder="请输入作业主体名称"
          style="width: 280px"
        ></el-input>
      </el-form-item>
      <div>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button type="default" @click="onReset">重置</el-button>
      </div>
    </el-form>

    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus" />
        新建作业主体
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="id"
        label="作业主体ID"
        min-width="120"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="name"
        label="作业主体名称"
        min-width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="socialCreditCode"
        label="社会信用代码"
        min-width="180"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        min-width="160"
        :formatter="formatDateTime"
      ></el-table-column>
      <el-table-column
        prop="modifyTime"
        label="最后修改时间"
        min-width="160"
        :formatter="formatDateTime"
      ></el-table-column>
      <el-table-column label="状态" min-width="100">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.disabled ? 'danger' : 'success'"
            size="small"
          >
            {{ scope.row.disabled ? '已禁用' : '正常' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" min-width="100">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleEdit(scope.row)">
            编辑/查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 10,
        // sorts: [
        //   {
        //     field: '',
        //     direction: ''
        //   }
        // ],
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          name: ''
          // socialCreditCode: '',
          // id: 0,
          // createTimeBegin: '',
          // createTimeEnd: '',
          // corporationIds: []
        }
      },
      total: 0,
      data: [],
      loading: true
    }
  },
  async created() {
    await this.getList()
  },
  methods: {
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.conditions.filters.name = ''
      this.onSearch()
    },

    async getList() {
      this.loading = true

      const [err, r] = await client.listCorporation({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },

    formatDateTime(row, column, cellValue) {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString('zh-CN')
    },

    handleAdd() {
      this.$router.push('/corporations/new')
    },

    handleView(row) {
      // TODO: 实现查看详情功能
      console.log('查看作业主体:', row)
    },

    handleEdit(row) {
      this.$router.push(`/corporations/${row.id}/edit`)
    },

    handleToggleStatus(row) {
      // TODO: 实现启用/禁用功能
      console.log('切换状态:', row)
    }
  }
}
</script>
