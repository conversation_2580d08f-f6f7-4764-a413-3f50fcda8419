<template>
  <div
    class="personnelInfoSubmission"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 10px;
        background: var(--o-primary-bg-color);
        padding: 20px 20px 0 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="90px"
    >
      <div class="lite" style="display: flex; align-items: center">
        <div>
          <el-form-item label="作业主体">
            <el-select
              filterable
              v-model="conditions.filters.supplierCorporationId"
              placeholder="请选择所属作业主体"
              style="width: 280px"
              clearable
            >
              <el-option
                v-for="item in supplierOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>

        <div style="text-align: right; flex: 1; position: relative; top: -11px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
    </el-form>
    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="handleExport"> 导出报送表 </el-button>
    </div>
    <div class="table-container">
      <el-table
        v-loading="loading"
        size="small"
        :data="data"
        height="100%"
        :header-cell-style="{
          'font-size': '12px',
          'font-weight': '400',
          color: '#777c94',
          background: 'var(--o-primary-bg-color)'
        }"
      >
        <el-table-column
          prop="id"
          label="人员信息报送表ID"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.id) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="supplierCorporationName"
          label="作业主体名称"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.supplierCorporationName) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="reportStatus"
          label="报送状态"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span :class="['status-tag', getReportStatusClass(scope.row.reportStatus)]">
              {{ getReportStatusText(scope.row.reportStatus) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="registrationLicenseObtained"
          label="是否已取得登记证照"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.registrationLicenseObtained) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="名称（姓名）"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.name) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="unifiedSocialCreditCode"
          label="统一社会信用代码（纳税人识别号）"
          min-width="180"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.unifiedSocialCreditCode) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="professionalServiceAgencyFlag"
          label="专业服务机构标识"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.professionalServiceAgencyFlag) }}
          </template>
        </el-table-column>
         <el-table-column
          prop="laborName"
          label="姓名"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.laborName) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="certificateType"
          label="证件类型"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ getCertificateTypeText(scope.row.certificateType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="idCard"
          label="身份证号"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.idCard) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="householdCity"
          label="国家或地区"
          min-width="180"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.householdCity) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="incomeReportingExemptionFlag"
          label="是否存在免于报送收入信息情形"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.incomeReportingExemptionFlag) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="exemptionType"
          label="免税类型"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.exemptionType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="householdAddress"
          label="地址"
          min-width="200"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.householdAddress) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="storeName"
          label="店铺（用户）名称"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.storeName) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="storeUniqueCode"
          label="店铺（用户）唯一标识码"
          min-width="200"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.storeUniqueCode) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="websiteUrl"
          label="网址链接（连接）"
          min-width="180"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.websiteUrl) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="cardBank"
          label="开户银行/非银行支付机构"
          min-width="200"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.cardBank) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="accountName"
          label="账户名称"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.accountName) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="bankCard"
          label="银行账号/支付账户"
          min-width="180"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.bankCard) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="contactName"
          label="联系人姓名"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.contactName) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="contactPhone"
          label="联系电话"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.contactPhone) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="startDate"
          label="经营开始日期"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.startDate) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="endDate"
          label="经营结束日期"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.endDate) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="infoStatusFlag"
          label="信息状态标记"
          min-width="120"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.infoStatusFlag) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="创建时间"
          min-width="180"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="modifyTime"
          label="修改时间"
          min-width="180"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ formatText(scope.row.modifyTime) }}
          </template>
        </el-table-column>

      </el-table>
    </div>

    <div style="text-align: right; padding: 20px 0; flex: 0 0 auto">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page="Math.floor(conditions.offset / conditions.limit) + 1"
        :page-size="conditions.limit"
        layout="total, prev, pager, next"
        :total="total"
      ></el-pagination>
    </div>

    <!-- 导出报送表对话框 -->
    <el-dialog
      title="导出报送表"
      :visible.sync="exportDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="exportForm"
        :rules="exportRules"
        ref="exportForm"
        label-width="100px"
      >
        <el-form-item label="作业主体" prop="supplierCorporationId" required>
          <el-select
            filterable
            v-model="exportForm.supplierCorporationId"
            placeholder="请选择所属作业主体"
            style="width: 300px"
            clearable
          >
            <el-option
              v-for="item in supplierOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="exportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmExport" :loading="exporting"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import { getToken } from '../../../helpers/token'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          id: '',
          supplierCorporationId: '',
          status: '',
          createTimeStart: null,
          createTimeEnd: null
        }
      },
      total: 0,
      data: [],
      loading: true,
      supplierOptions: [],
      // 导出报送表对话框相关
      exportDialogVisible: false,
      exporting: false,
      exportForm: {
        supplierCorporationId: ''
      },
      exportRules: {
        supplierCorporationId: [
          {
            required: true,
            message: '请选择作业主体',
            trigger: ['change', 'blur']
          }
        ]
      }
    }
  },
  async created() {
    await this.loadSupplierOptions()
    await this.getList()
  },
  methods: {
    // 格式化文本显示
    formatText(value) {
      return value || '-'
    },
    // 加载作业主体选项
    async loadSupplierOptions() {
      try {
        const [, response] = await client.listCorporation({
          body: { filters: {} }
        })

        if (response && response.success && response.data) {
          this.supplierOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载作业主体选项失败：', error)
      }
    },
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },
    onReset() {
      this.conditions = {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          id: '',
          supplierCorporationId: '',
          status: '',
          createTimeStart: null,
          createTimeEnd:  null
        }
      }
      this.getList()
    },

    async getList() {
      this.loading = true

      const queryConditions = { ...this.conditions }

      const [err, r] = await client.infoLaborList({
        body: queryConditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
    },
    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },
    handleExport() {
      // 重置表单
      this.exportForm = {
        supplierCorporationId: ''
      }
      this.$nextTick(() => {
        if (this.$refs.exportForm) {
          this.$refs.exportForm.clearValidate()
        }
      })
      this.exportDialogVisible = true
    },
    async confirmExport() {
      // 验证表单
      const valid = await this.$refs.exportForm.validate().catch(() => false)
      if (!valid) {
        return
      }

      this.exporting = true

      try {
        // 使用fetch API携带token下载文件
        const token = `Bearer ${getToken()}`
        const response = await fetch(
          `${window.env?.apiPath}/api/supplier/infolabor/download`,
          {
            method: 'POST',
            headers: {
              Authorization: token,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              supplierCorporationId: this.exportForm.supplierCorporationId
            })
          }
        )

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 获取文件blob
        const blob = await response.blob()

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        // 获取当前日期作为文件名的一部分
        const now = new Date()
        const dateStr = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`

        link.download = `人员信息报送表_${dateStr}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理URL对象
        window.URL.revokeObjectURL(url)

        this.$message.success('报送表导出成功')
        this.exportDialogVisible = false
      } catch (error) {
        console.error('导出报送表失败：', error)
        this.$message.error('导出报送表失败')
      } finally {
        this.exporting = false
      }
    },
    // 获取报送状态文本
    getReportStatusText(status) {
      const statusMap = {
        PENDING_REPORT: '待报送'
      }
      return statusMap[status] || status
    },
    // 获取报送状态样式类
    getReportStatusClass(status) {
      const classMap = {
        PENDING_REPORT: 'status-default'
      }
      return classMap[status] || 'status-default'
    },
    getCertificateTypeText(type) {
      const typeMap = {
        'ID_CARD': '居民身份证',
        'PASSPORT': '护照',
        'HONG_KONG_MACAO_TAIWAN': '港澳台同胞回乡证',
        'FOREIGNER': '外国人永久居留证'
      }
      return typeMap[type] || '居民身份证'
    },
  }
}
</script>

<style scoped>
/* 表格容器 */
.table-container {
  flex: 1 1 auto;
  min-height: 0;
  overflow: hidden;
}


/* 状态标签样式 */
.status-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
}

.status-pending-report {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-default {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #d9d9d9;
}
</style>
