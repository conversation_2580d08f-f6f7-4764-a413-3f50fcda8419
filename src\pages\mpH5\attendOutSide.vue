<template>
  <div class="outSide">
    <div id="map" style="background: #fff; height:200px; overflow: auto;">
      <!-- <img
        width="100%"
        style="display:block;"
        :src="staticMapURL"
      /> -->
      <div :style="{
        background:'url('+staticMapURL+') no-repeat center',
        backgroundSize:'cover',
        width:'100%',
        height:'300px',
        backgroundPosition: 'center'
      }">

      </div>
    </div>
    <CheckInPopup
      ref="checkInPopup"
      title="确认外勤打卡吗？"
      actionTitle="外勤打卡"
      :address="address"
      :defaultShown="true"
      :phonesRequired="phonesRequired"
      :commentRequired="commentRequired"
      @close="$router.back()"
      @upload="handleUpload"
      @checkIn="handleCheckIn"
      :overlay="false"
    />
  </div>
</template>

<script>
import CheckInPopup from '../../components/mpH5/attend/checkInPopup.vue'
import makeClient from 'kit/services/platform/makeClient'
import handleError from 'kit/helpers/handleErrorH5'
import handleSuccess from 'kit/helpers/handleSuccessH5'
import { isObject, isArray } from 'kit/helpers'
const platformClient = makeClient()
var checking = false
var command = null
export default {
  components: {
    CheckInPopup
  },
  data() {
    return {
      address: '',
      phonesRequired: false,
      commentRequired: false,
      images: [],
      command:{}
    }
  },
  computed:{
    staticMapURL(){
      const {latitude,longitude} = this.command
      const center = [longitude,latitude].join(",")
      const tk = 'e6f7e78bf1415e24d9c4d6dabe2d8c16'
      const zoom = 17
      return` https://api.tianditu.gov.cn/staticimage?center=${center}&zoom=${zoom}&width=100%&height=200&zoom=10&tk=${tk}&markers=${center}`
    }
  },  
  mounted() {
    const commandStr = sessionStorage.getItem('checkInCommand')
    command = JSON.parse(commandStr)
    this.command = command
    console.log(command)

    if (!isObject(command)) {
      handleError('无法解析打卡命令')
      this.$router.back()
      return
    }

    this.address = command.recordAddress

    const attendGroup = JSON.parse(sessionStorage.getItem('attendGroup'))
    this.phonesRequired = attendGroup.outsideImages
    this.commentRequired = attendGroup.outsideRemark
  },
  methods: {
    handleCheckIn(comment) {
      if (this.phonesRequired && !this.images.length) {
        handleError('请输入照片')
        return
      }
      if (this.commentRequired && !comment.trim()) {
        handleError('请输入备注')
        return
      }

      command.signImages = JSON.stringify(this.images)
      command.signDescription = comment

      console.log('command', command)
      this.doCheckIn(command)
    },
    async doCheckIn(command) {
      if (checking) {
        return
      }
      checking = true

      const [err, r] = await platformClient.attendMobileSign({
        body: command
      })
      checking = false
      if (err) {
        handleError(err)
        return
      }

      if (r.success) {
        handleSuccess('打卡成功')
        this.$router.back()
      }
    },
    async handleUpload(files) {
      var images = []
      if (isArray(files)) {
        for (var c of files) {
          const image = await this.uploadFile(c.file)
          images.push(image)
        }
      } else {
        const image = await this.uploadFile(files.file)
        images.push(image)
      }

      this.images = images
    },
    async uploadFile(file) {
      var formData = new FormData()
      formData.set('file', file)
      const [err, r] = await platformClient.attendMobileSignFilsUpload({
        body: formData,
        requestInterceptor(resource, options) {
          delete options.headers['Content-Type']
          return [null, resource, options]
        }
      })
      if (err) {
        handleError(err)
        return
      }

      return r.data
    }
  }
}
</script>

<style>
</style>