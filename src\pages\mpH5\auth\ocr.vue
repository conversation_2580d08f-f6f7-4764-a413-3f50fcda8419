<template>
  <div class="ocrH5">
    <OcrStep1H5 v-show="step === 0" @ocr="ocr" />
    <OcrStep2H5 v-show="step === 1" v-model="certificationInfo" />
  </div>
</template>

<script>
import OcrStep1H5 from './ocr/ocrStep1.vue'
import OcrStep2H5 from './ocr/ocrStep2.vue'
import stringToDate from './stringToDate'
export default {
  components: {
    OcrStep1H5,
    OcrStep2H5
  },
  data() {
    return {
      step: 0,
      certificationInfo: {}
    }
  },
  methods: {
    ocr(info) {
      this.step = 1
      const birth = stringToDate(info.birth)

      let validDate = '',
        validDate1 = '',
        validDate2 = ''

      if (info.validDate?.includes('-')) {
        validDate = info.validDate.split('-')
        validDate1 = stringToDate(validDate[0])
        validDate2 = stringToDate(validDate[1])
      }

      this.certificationInfo = { ...info, birth, validDate1, validDate2 }
    }
  }
}
</script>

<style>
</style>