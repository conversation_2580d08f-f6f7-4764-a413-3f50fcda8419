<template>
  <div
    style="width: 319px; position: relative; display: flex; align-items: center"
  >
    <Field
      placeholder="请输入验证码"
      clearable
      maxlength="6"
      size="large"
      :label="label"
      type="tel"
      :rules="rules"
      @input="changeAnswer"
      @clear="clearAnswer"
      v-model="otp.answer"
    >
      <template #button>
        <Button
          class="sms-button"
          size="small"
          type="primary"
          plain
          :loading="isLoading"
          @click="send"
          v-show="!countdown"
        >
          获取验证码
        </Button>
        <div v-show="countdown">
          <Countdown
            template="%ds后重新获取"
            ref="countdown"
            @finish="countdown = false"
          />
        </div>
      </template>
    </Field>
  </div>
</template>

<script>
import { Field, Button } from 'vant'
import Countdown from '../../components/ui/countdown.vue'
import handleError from '../../helpers/handleErrorH5'
import makePlatformClient from '../../services/platform/makeClient'

const platformClient = makePlatformClient()

export default {
  props: {
    phone: {
      type: String,
      default: ''
    },
    label: {
      tye: String,
      default: ''
    },
    captcha: {
      type: Object,
      default() {
        return {
          answer: '',
          token: ''
        }
      }
    },
    rules: Array
  },
  components: {
    Field,
    Button,
    Countdown
  },
  data() {
    return {
      countdown: false,
      otp: {
        answer: '',
        token: ''
      },
      isLoading: false
    }
  },
  methods: {
    async send() {
      if (!this.phone) {
        handleError('请输入手机号')
        return
      }

      if (!this.captcha.token || !this.captcha.answer) {
        handleError('请先完成图形验证码')
        return
      }

      this.isLoading = true
      await this._send()
    },
    async _send() {
      const [err, r] = await platformClient.merchantPlatformCreateOtp({
        body: {
          otpType: 'SMS',
          receiver: this.phone,
          captchaToken: this.captcha.token,
          captchaAnswer: this.captcha.answer
        }
      })

      this.isLoading = false

      if (err) {
        handleError(err)
        return
      }

      if (!r.data) {
        handleError('验证码获取失败')
        return
      }

      this.countdown = true
      this.$refs.countdown.start()

      this.otp.token = r.data.token
      this.changeAnswer(this.otp.answer)
    },
    changeAnswer(v) {
      if (this.otp.token) {
        this.$emit('input', {
          token: this.otp.token,
          answer: this.otp.answer
        })
      } else {
        this.$emit('input', {
          answer: this.otp.answer
        })
      }
    },
    clearAnswer() {
      this.answer = ''
      this.$emit('input', {})
      this.load()
    }
  }
}
</script>

<style scoped>
::v-deep .sms-button {
  border: 0;
  color: #a6aebd !important;
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC';
}
</style>