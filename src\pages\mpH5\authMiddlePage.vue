<template>
  <div
    class="authenticationBoHaiH5"
    style="
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
      flex-direction: column;
    "
  >
    <img style="width: 40px" src="../../assets/images/bohai_logo.gif" alt="" />
    <p
      style="
        text-align: center;
        font-size: 16px;
        margin: 0;
        margin-top: 5px;
        margin-left: 10px;
      "
    >
      跳转中...
    </p>
  </div>
</template>

<script>
import handleError from '../../helpers/handleErrorH5'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  async created() {
    // this.$router.replace('/login?openId='+'ocvSz0gASFLed7CnBbZAR027G8vs')
    this.init()
  },
  methods: {
    async init() {
      const code = this.$route.query.code
      const [err, r] = await platformClient.wxGetOpenId({ body: { code } })
      if (err) {
        handleError(err)
        return
      }

      const openid = r.appId
      this.$router.replace('/login?openId=' + openid)
    }
  }
}
</script>

<style scoped >
.authenticationBoHaiH5 {
  background: url('../../assets/images/bohai_middenPage_bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>