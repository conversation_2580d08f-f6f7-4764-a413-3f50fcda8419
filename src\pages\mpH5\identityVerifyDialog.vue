<template>
<!-- 未完成 -->
  <div>
    <Popup
      v-model="show"
      @close="close"
      round
      closeable
      :style="{ height: '220px', width: '300px', textAlign: 'center' }"
    >
      <Captcha style="width: 100%" v-model="captcha" />
      <Otp
        style="width: 100%"
        v-model="otp"
        :captcha="captcha"
        :phone="phone"
      />
      <p>若该手机号已无法使用或收不到短信验证码请联系客服</p>
      <div>
        <Button style="width: 100px; margin-right: 10px" @click="close"
          >取消</Button
        >
        <Button type="primary" style="width: 100px" @click="confirm"
          >确定</Button
        >
      </div>
    </Popup>
  </div>
</template>

<script>
import { Popup, Button } from 'vant'
import Captcha from './captcha.vue'
import Otp from './otp.vue'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient() 

export default {
  name: 'IdentityVerify',
  components: {
    Captcha,
    Otp,
    <PERSON><PERSON>,
    But<PERSON>
  },
  data() {
    return {
      show: false,
      phone: '',
      captcha: {
        answer: '',
        token: ''
      },
      otp: {
        answer: '',
        token: ''
      }
    }
  },
  async mounted() {
    const [err, r] = await platformClient.merchantPlatformProfile({
      body: {}
    })

    if (err) {
      handleError(err)
      return
    }

    this.phone = r.data
  },
  methods: {
    close() {
      this.show = false

      this.captcha = {
        answer: '',
        token: ''
      }

      this.otp = {
        answer: '',
        token: ''
      }
    },
    open() {
      this.show = true
    }
  }
}
</script>

<style>
</style>