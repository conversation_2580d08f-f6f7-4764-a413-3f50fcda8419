<template>
  <div class="step1-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>{{ isEdit ? '编辑合同模板' : '新建合同模板' }}</h2>
    </div>

    <el-form
      ref="templateForm"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      style="max-width: 600px"
    >
      <el-form-item label="模板文件" prop="archiveId">
        <file-uploader
          v-model="formData.archiveId"
          :max-count="1"
          accept=".pdf"
          placeholder="请上传合同模板文件"
          @file-change="handleFileChange"
        />
        <div class="form-tip">支持 PDF 格式文件，文件大小不超过 10MB</div>
      </el-form-item>

      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="formData.templateName"
          placeholder="请输入模板名称"
          maxlength="50"
          show-word-limit
          @input="updateParentData"
        />
      </el-form-item>

      <el-form-item label="作业主体" prop="corporationIds">
        <div style="display: flex; gap: 10px; align-items: center">
          <el-select
            style="flex: 1"
            v-model="formData.corporationIds"
            placeholder="请选择作业主体"
            multiple
            :loading="loading"
            :remote="remote"
            :remote-method="handleRemoteSearch"
          >
            <el-option
              v-for="item in options"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
              <span style="float: left">{{ item.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ item.socialCreditCode }}
              </span>
            </el-option>
          </el-select>
        </div>
        <div class="form-tip">可选择多个作业主体</div>
      </el-form-item>
      <el-form-item label="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleNext">
          下一步
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import FileUploader from './uploader/file.vue'
import CorporationsSelector from './selector/corporations.vue'
import makeClient from '../../../services/operateLabor/makeClient'
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'

const client = makeClient()

export default {
  name: 'ContractTemplateStep1',
  components: {
    FileUploader,
    CorporationsSelector
  },

  data() {
    return {
      isEdit: false,
      templateId: null,
      formData: {
        archiveId: '',
        tempId: 0,
        templateName: '',
        templateType: 'OTHERS',
        corporationIds: [],
        steps: [
          {
            stepId: 0,
            operate: 'SIGN',
            stepName: '个人签署方',
            compEmpName: '',
            compEmpId: '',
            filedList: [],
            radioVal: '2',
            signIndex: 0,
            sortBy: 0
          },
          {
            stepId: 0,
            operate: 'SEAL',
            stepName: '企业签署方',
            compEmpName: '',
            compEmpId: '',
            filedList: [],
            radioVal: '1',
            sortBy: 1
          }
        ]
      },
      formRules: {
        archiveId: [
          { required: true, message: '请上传模板文件', trigger: 'change' }
        ],
        templateName: [
          { required: true, message: '请输入模板名称', trigger: 'blur' },
          {
            min: 2,
            max: 50,
            message: '模板名称长度为2-50个字符',
            trigger: 'blur'
          }
        ],
        corporationIds: [
          { required: true, message: '请选择作业主体', trigger: 'change' },
          {
            type: 'array',
            min: 1,
            message: '至少选择一个作业主体',
            trigger: 'change'
          }
        ]
      },
      submitting: false,
      loading: false,
    }
  },

  async created() {
    // 检查是否为编辑模式
    if (this.$route.params.id) {
      this.isEdit = true
      this.templateId = parseInt(this.$route.params.id)
      await this.loadTemplateDetail()
    }

    this.loadCorporations()
  },

  methods: {
    handleRemoteSearch(keyword) {
      if (keyword) {
        this.loadCorporations(keyword)
      } else {
        this.loadCorporations()
      }
    },
    async loadCorporations(keyword = '') {
      this.loading = true

      try {
        const [err, response] = await client.listCorporation({
          body: {
            offset: 0,
            limit: 100,
            withTotal: false,
            withDisabled: true, // 包含已禁用的，以防编辑时选中的是已禁用的
            withDeleted: false,
            filters: {
              name: keyword
            }
          }
        })

        if (err) {
          handleError(err)
          return
        }

        this.options = response.data?.list || []
      } catch (error) {
        handleError(error)
      } finally {
        this.loading = false
      }
    },
    handleFileChange(file) {
      if (file) {
        this.formData.templateName = file.name.replace('.pdf', '')
      }
      this.updateParentData()
    },
    // 手动更新父组件数据
    updateParentData() {
      this.$emit('input', this.formData)
    },
    async loadTemplateDetail() {
      try {
        const [err, response] = await client.getTemplateDetail(
          this.templateId,
          {}
        )

        if (err) {
          handleError(err)
          return
        }

        const templateData = response.data
        this.formData = {
          ...this.formData,
          archiveId: templateData.archiveId || '',
          tempId: templateData.tempId,
          templateName: templateData.templateName,
          templateType: templateData.templateType,
          corporationIds: templateData.corporationIds || [],
          steps: templateData.steps || this.formData.steps
        }
      } catch (error) {
        handleError(error)
      }
    },

    handleAddCorporation() {
      // 跳转到新建作业主体页面
      this.$router.push('/corporations/new')
    },

    handleCancel() {
      this.$router.push('/contractTemplates')
    },

    async handleNext() {
      const valid = await this.$refs.templateForm.validate()
      if (!valid) return
      this.submitting = true
      const submitData = {
        ...this.formData,
        tempId: this.isEdit ? this.templateId : null,
        templateType: 'OTHERS'
      }

      const [err, r] = await client.createTemplate({
        body: submitData
      })
      this.submitting = false
      if (err) {
        handleError(err)
        return
      }

      handleSuccess(this.isEdit ? '模板更新成功' : '模板创建成功')
      const templateId = this.templateId ? this.templateId : r.data.data * 1

      const [err1, r1] = await client.setTemplate({
        body: {
          data: templateId
        }
      })
      if (err1) {
        handleError(err1)
        return
      }

      const { token, url } = r1.data

      window.open(`${url}?token=${token}`)
      this.$router.push('/contractTemplates')
    }
  }
}
</script>

<style scoped>
.step1-container {
  padding: 20px;
  background: #fff;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.form-tip {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

::v-deep .el-form-item__label {
  font-weight: 500;
}

::v-deep .el-form-item {
  margin-bottom: 24px;
}
</style>
