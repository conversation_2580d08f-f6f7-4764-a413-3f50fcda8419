<template >
  <div class="emailNew" style="">
    <template>
      <Field
        v-model="email"
        placeholder="请输入邮箱地址"
        label="验证邮箱"
        readonly
      />
      <Field
        v-model="code"
        maxlength="6"
        placeholder="请输入邮箱验证码"
        label="邮箱验证码"
      >
        <template #button>
          <Button
            class="sms-button"
            size="small"
            type="primary"
            plain
            :loading="isLoading"
            @click="send"
            v-show="!countdown"
          >
            获取验证码
          </Button>
          <div v-show="countdown">
            <Countdown
              template="%ds后重新获取"
              ref="countdown"
              @finish="countdown = false"
            />
          </div>
        </template>
      </Field>

      <div @click="goEmailForget" style="margin: 12px;font-size:14px;color:#409EFF">
        邮箱无法认证
      </div>

      <div style="margin: 16px">
        <Button @click="submit" round block type="primary">确定</Button>
      </div>
    </template>
  </div>
</template>

<script>
import { Form, Field, Button } from 'vant'
import Countdown from '../../components/ui/countdown.vue'
import makePlatformClient from '../../services/platform/makeClient'
import handleError from '../../helpers/handleErrorH5'
import handleSuccess from '../../helpers/handleSuccessH5'
const platformClient = makePlatformClient()

export default {
  components: {
    Form,
    Field,
    Button,
    Countdown
  },
  data() {
    return {
      code: '',
      email: '',
      token: '',
      countdown: false,
      isLoading: false
    }
  },

  created(){
    this.email = this.$route.query.email
  },

  methods: {
    async send() {

      this.isLoading = true

      const [err, r] = await platformClient.merchantEmailSend({
        body:{
          email: this.email
        }
      })

      if (err) {
        this.isLoading = false
        handleError(err)
        return
      }

      this.countdown = true
      this.isLoading = false
      this.token = r.data.emailToken
    },
    async submit() {
      if(!this.token){
        handleError('请先获取验证码')
        return
      }

      if(!this.code){
        handleError('请输入邮箱验证码')
        return
      }

      const [err, r] = await platformClient.merchantEmailUpdateVerify({
        body: {
          authCode: this.code,
          email: this.email,
          token:this.token,
          authCodeType:'EMAIL'
        }
      })

      if (err) {
        handleError(err)
        return
      }

      handleSuccess('验证成功')
      this.$router.replace('/emailNew')
    },
    goEmailForget(){
      this.$router.push('/emailForgetChange')
    }
  }
}
</script>

<style>
</style>