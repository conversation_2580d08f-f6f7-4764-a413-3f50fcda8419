<template>
  <div class="uploader">
    {{ error }}
    <!-- Slot for the button that triggers file selection -->
    <div
      @click="triggerFileSelect"
      style="display: inline-block; cursor: pointer"
    >
      <slot name="button">
        <a style="">上传文件</a>
      </slot>
    </div>

    <!-- Hidden file input -->
    <input
      ref="fileInput"
      type="file"
      :multiple="multiple"
      :accept="accept"
      @change="handleFileChange"
      style="display: none"
    />
    {{ files }}
    <slot :files="files" :removeFile="removeFile"> </slot>
  </div>
</template>

<script>
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'Uploader',
  computed: {
    isMaxCountReached() {
      const successCount = this.files.filter(
        item => item.status === 'success'
      ).length

      const uploadingCount = this.files.filter(
        item => item.status === 'uploading'
      ).length

      return successCount + uploadingCount >= this.maxCount
    }
  },
  props: {
    value: {
      type: [String, Array],
      default: ''
    },
    multiple: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'file'
    },
    accept: {
      type: String,
      default: '.*'
    },
    maxSize: {
      type: Number,
      default: 10 // MB
    },
    maxCount: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      uploading: false,
      files: [],
      error: ''
    }
  },
  methods: {
    triggerFileSelect() {
      if (this.uploading) return
      this.$refs.fileInput.click()
    },

    handleFileChange(event) {
      const selectedFiles = Array.from(event.target.files)
      if (!selectedFiles.length) return

      const remain = this.maxCount - this.files.length
      if (selectedFiles.length + this.files.length > this.maxCount) {
        this.handleError(`超出上传数量限制，您还可以上传 ${remain} 个文件`)
        return
      }
      debugger
      var needUploadFiles = selectedFiles
      //单选 不能超过1
      if (!this.multiple && selectedFiles.length > 0) {
        needUploadFiles = selectedFiles.slice(0, 1)
      }

      this.uploadFiles(needUploadFiles)
    },

    async uploadFiles(needUploadFiles) {
      var errors = needUploadFiles.map(this.validateFile)
      if (errors.find(c => c)) {
        this.handleError(errors.filter(c => c).join('\n'))
        return
      }

      this.loading = true
      for (const file of needUploadFiles) {
        const formData = new FormData()
        formData.append('file', file)

        const fileItem = {
          id: null,
          name: file.name,
          status: 'uploading',
          file: file
        }
        const [err, r] = await this.uploadFile(fileItem)
        if (err) {
          fileItem.status = 'failed'
          fileItem.error = err.message
          errors.push(err.message)
          this.files.push(fileItem)
          continue
        }

        fileItem.id = r.data.fileId
        fileItem.status = 'succeed'
        if (this.type === 'image') {
          fileItem.url = this.imageURL(fileItem.id)
        }
        if (this.type === 'file') {
          fileItem.url = this.fileURL(fileItem.id)
        }

        this.files.push(fileItem)
      }

      this.loading = false
      this.emitValue()
    },
    async uploadFile(fileItem) {
      const formData = new FormData()
      formData.append('file', fileItem.file)
      return await client.uploadFile({
        body: formData
      })
    },
    validateFile(file) {
      if (file.size > this.maxSize * 1024 * 1024) {
        return `文件 "${file.name}" 大小超过 ${this.maxSize}MB`
      }
      if (this.accept === '.*') {
        return ''
      }

      const acceptedExts = this.accept
        .split(',')
        .map(t => t.trim().toLowerCase())
      const fileExtension = '.' + file.name.split('.').pop().toLowerCase()

      const isValid = acceptedExts.some(type => {
        return fileExtension === type
      })

      if (!isValid) {
        return `文件 "${file.name}" 的格式不被接受`
      }

      return ''
    },
    handleError(error) {
      this.error = error
      this.$emit('onError', error)
    },

    fileURL(fileId) {
      return `${window.env?.apiPath}/api/public/downloadFile/${fileId}`
    },
    imageURL(fileId) {
      return `${window.env?.apiPath}/api/public/previewFile/${fileId}`
    },
    removeFile(file) {
      this.files = this.files.filter(file => file.uid !== fileToRemove.uid)
      this.emitValue()
    },
    emitValue() {
      const successFiles = this.files
        .filter(file => file.status === 'succeed')
        .map(file => file.response.data.fileId)

      if (this.multiple) {
        this.$emit('input', successFiles)
      } else {
        this.$emit('input', successFiles.length > 0 ? successFiles[0] : '')
      }
    }
  }
}
</script>
