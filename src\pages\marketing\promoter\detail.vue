<template>
  <div style="padding: 13px 12px 84px; min-height: 100vh; background: #f2f4f7">
    <div class="banner" style="position: relative">
      <VantImage
        width="100%"
        height="100%"
        :src="activityInfo.bannerImageUrl"
      />
    </div>
    <div
      style="
        display: flex;
        flex-direction: column;
        padding: 0.16rem 0.32rem;
        background: #ffffff;
        border-radius: 8px;
        border: 0.5px solid #ffffff;
        position: relative;
        top: -0.24rem;
      "
    >
      <div
        style="
          font-size: 16px;
          color: #1e2228;
          margin: 12px 0;
          font-weight: 500;
        "
      >
        活动信息
      </div>
      <div class="text" style="align-items: center">
        <span>活动时间</span>
        <div style="flex: 1; color: #1e2228">
          {{ activityTime }}
        </div>
      </div>

      <div class="text">
        <span>达标资格</span>
        <div style="flex: 1; color: #1e2228">
          {{ activityInfo.qualification }}
        </div>
      </div>

      <div class="text">
        <span>达标奖励</span>
        <div style="flex: 1; color: #1e2228">
          {{ activityInfo.rewardInfo }}
        </div>
      </div>

      <div class="text">
        <span style="display: block; width: 70px">备注</span>
        <div style="flex: 1; color: #1e2228">
          {{ activityInfo.remark }}
        </div>
      </div>
    </div>
    <div class="title" v-if="activityInfo.status === '2'">
      <span style="flex: 0 0 96px">活动二维码</span>
      <a style="flex: 1; color: #f77234" @click="showQrCode">展开二维码</a>
    </div>
    <div
      class="title"
      style="margin: 12px 0"
      v-if="activityInfo.status === '2'"
    >
      <span style="flex: 0 0 96px">活动链接</span>
      <a style="flex: 1; color: #f77234" @click="copyLink">点击复制</a>
    </div>
    <div class="title">
      <span style="flex: 0 0 96px">执行情况</span>
      <div @click="info" style="flex: 1; display: flex; align-items: center">
        <a style="flex: 1; color: #1e2228; margin-right: 109px"
          >查看已发放明细
        </a>
        <i
          style="color: #000000"
          class="icon iconfont icon-direction-arrow-border-right"
        ></i>
      </div>
    </div>
    <Button
      style="width: 100%; margin-top: 30px; border-radius: 6px"
      @click="goBack"
      type="primary"
      size="middle"
    >
      返回
    </Button>

    <Dialog v-model="isShowDialog">
      <ExpandQRCode
        ref="expandQRCode"
        :id="id"
        :promoterName="promoterName"
        :qrCodeURL="qrCodeURL"
        :leftCount="leftCount"
        @refreshQrCode="showQrCode"
        @closeQrCode="isShowDialog = false"
      />
    </Dialog>
  </div>
</template>
<script>
import { Dialog, Toast, Image, Button } from 'vant'
import ExpandQRCode from 'kit/components/marketing/promoter/expandQRCode.vue'
import copyText from 'kit/helpers/copyText'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()
import handleErrorH5 from 'kit/helpers/handleErrorH5'

export default {
  components: {
    Dialog: Dialog.Component,
    VantImage: Image,
    Button,
    ExpandQRCode
  },
  data() {
    return {
      id: this.$route.query.id,
      promoterName: this.$route.query.promoterName,
      isShowDialog: false,
      qrCodeURL: '',
      leftCount: 0,
      activityInfo: {},
      activityTime: '',
      qrCodeCopyURL: ''
    }
  },
  async created() {
    const [err, r] = await marketingClient.mobilePromoterActivityInfo({
      body: {
        id: this.id
      }
    })
    if (err) {
      handleErrorH5(err)
      return
    }

    document.title = r.data.name
    this.activityInfo = r.data
    this.activityTime =
      this.activityInfo.availableBeginTime.split(' ')[0] +
      ' ~ ' +
      this.activityInfo.availableEndTime.split(' ')[0]

    const [err1, result] =
      await marketingClient.mobilePromoterActivityMyActivityCode({
        body: {
          id: this.id
        }
      })
    if (err1) {
      handleErrorH5(err1)
      return
    }
    this.qrCodeCopyURL = result.data.url
  },
  methods: {
    async showQrCode() {
      const [err, r] =
        await marketingClient.mobilePromoterActivityMyActivityCode({
          body: {
            id: this.id
          }
        })
      if (err) {
        handleErrorH5(err)
        return
      }
      this.qrCodeURL = `data:image/png;base64,${r.data.qrCode}`
      this.leftCount = r.data.leftCount
      this.isShowDialog = true
      this.$nextTick(() => {
        this.$refs.expandQRCode.start()
      })
    },
    async copyLink() {
      copyText(this.qrCodeCopyURL)
      Toast.success('复制成功')
    },
    info() {
      this.$router.push({
        path: '/distributionDetails',
        query: { id: this.$route.query.id }
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>
<style scoped>
.text {
  display: flex;
  margin-bottom: 8px;
  color: #828b9b;
  font-size: 14px;
}
.text > span {
  flex: 0 0 84px;
  margin-right: 12px;
}
.title {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  color: #828b9b;
  background: #ffffffff;
  border-radius: 8px;
  font-size: 14px;
}
::v-deep .van-dialog__footer {
  display: none;
}
::v-deep .van-image {
  display: block;
}
::v-deep .van-image__img {
  border-radius: 8px 8px 0 0;
}
</style>
