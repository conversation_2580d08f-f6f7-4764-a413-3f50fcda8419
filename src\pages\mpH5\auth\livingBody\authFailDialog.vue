<template>
  <Popup
    v-model="showPopup"
    round
    style="width: 300px; height: 300px; padding-top: 30px"
  >
    <h3 style="text-align: center; margin-bottom: 30px">身份认证失败</h3>
    <div style="padding: 0 10px 0 40px; margin-bottom: 40px">
      <p>请参照注意事项重新认证</p>
      <p>1.请确保本人操作且正脸对框</p>
      <p>2.请确保光线适中，不要太暗或太强</p>
      <p>3.请根据提示做出相应动作</p>
    </div>
    <div
      style="
        padding: 0 30px;
        display: flex;
        align-items: center;
        justify-content: center;
      "
    >
      <a
        @click="retry"
        style="
          color: #fff;
          background: #4f71ff;
          width: 130px;
          margin-right: 10px;
          height: 44px;
          border-radius: 30px;
          line-height: 44px;
          text-align: center;
        "
        >重试</a
      >
      <a
        @click="back"
        style="
          width: 130px;
          margin-right: 10px;
          height: 44px;
          border: 1px solid #4f71ff;
          border-radius: 30px;
          line-height: 44px;
          text-align: center;
          color: #4f71ff;
        "
        >返回</a
      >
    </div>
  </Popup>
</template>

<script>
import { Popup, Button } from 'vant'
export default {
  components: {
    Popup,
    Button
  },
  data() {
    return {
      showPopup: false
    }
  },
  methods: {
    open() {
      this.showPopup = true
    },
    close() {
      this.showPopup = false
    },
    retry() {
      this.close()
      this.$emit('retry')
    },
    back() {
      this.close()
      this.$emit('back')
    }
  }
}
</script>

<style scoped>
h3,
p {
  margin: 0;
}
p {
  margin-bottom: 10px;
}
</style>
