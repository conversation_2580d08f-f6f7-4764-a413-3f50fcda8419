<template>
  <div class="activities">
    <div class="tabBar">
      <div
        style="display: flex; flex-direction: column; align-items: center"
        v-for="(item, index) in tabBar"
        :key="index"
      >
        <span
          :class="{ active: activeItem === item.status }"
          @click="changeTab(item.status)"
          >{{ item.name }}</span
        >
        <div v-if="activeItem === item.status" class="active-style"></div>
      </div>
    </div>
    <div
      style="
        background: #eff2fa;
        min-height: 100vh;
        padding: 13px 12px 74px 12px;
        box-sizing: border-box;
      "
    >
      <div
        style="margin-bottom: 10px"
        :key="index"
        v-for="(activity, index) in activities"
      >
        <Activity
          class="item"
          v-if="activities.length"
          :linkUrl="linkUrl"
          @copyLink="copyLink(activity)"
          @showQrCode="showQrCode"
          :activity="activity"
        />
      </div>
      <div
        v-if="!activities.length"
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          margin-top: 230px;
        "
      >
        <img width="120px" src="../../../assets/images/no-data.svg" />
        <span style="color: #828b9bff; font-size: 14px">暂无活动</span>
      </div>
      <div
        style="width: 100vw; position: fixed; bottom: 0; background: #ffffff"
      >
        <Actions />
      </div>
      <Dialog v-model="isShowDialog">
        <ExpandQRCode
          ref="expandQRCode"
          :id="id"
          :promoterName="promoterName"
          :qrCodeURL="qrCodeURL"
          :leftCount="leftCount"
          @refreshQrCode="showQrCode"
          @closeQrCode="isShowDialog = false"
        />
      </Dialog>
    </div>
  </div>
</template>

<script>
import Activity from 'kit/components/marketing/promoter/activity.vue'
import ExpandQRCode from 'kit/components/marketing/promoter/expandQRCode.vue'
import Actions from 'kit/components/marketing/promoter/menus.vue'
import { Dialog, Toast } from 'vant'
import copyText from 'kit/helpers/copyText'
import store from 'kit/helpers/store'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()
import handleErrorH5 from 'kit/helpers/handleErrorH5'

export default {
  components: {
    Activity,
    ExpandQRCode,
    Actions,
    Dialog: Dialog.Component
  },
  data() {
    return {
      activeItem: 2,
      tabBar: [
        {
          name: '进行中',
          status: 2
        },
        {
          name: '待开始',
          status: 1
        },
        {
          name: '已结束',
          status: 3
        }
      ],
      isShowDialog: false,
      activities: [],
      linkUrl: '',
      qrCodeURL: '',
      show: false,
      id: '',
      promoterName: '',
      leftCount: ''
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (from.path === '/activityDetail') {
        vm.activeItem = parseInt(store.get('activeItem'))
      }
      vm.activitiesList(vm.activeItem)
    })
  },
  beforeRouteLeave(to, from, next) {
    store.set('activeItem', this.activeItem)
    next()
  },
  methods: {
    changeTab(status) {
      this.activeItem = status
      this.activitiesList(status)
    },
    async activitiesList(status) {
      const [err, r] =
        await marketingClient.mobilePromoterActivityQueryMyCouponsActivity({
          body: {
            filters: {
              status
            },
            start: 0,
            limit: 10000,
            sorts: [
              // {
              //   field: 'string',
              //   direction: 'ASCENDING'
              // }
            ],
            withTotal: true,
            withDisabled: true,
            withDeleted: true
          }
        })
      if (err) {
        handleErrorH5(err)
        return
      }
      this.activities = r.data.list
      this.setQrCodeURL()
    },

    // 获取/刷新 二维码
    async showQrCode(activity) {
      this.id = activity ? activity.id : this.id
      this.promoterName = activity ? activity.promoter.name : this.promoterName
      const [err, r] =
        await marketingClient.mobilePromoterActivityMyActivityCode({
          body: {
            id: activity ? activity.id : this.id
          }
        })
      if (err) {
        handleErrorH5(err)
        return
      }
      this.qrCodeURL = `data:image/png;base64,${r.data.qrCode}`
      this.leftCount = r.data.leftCount
      this.isShowDialog = true
      this.$nextTick(() => {
        this.$refs.expandQRCode.start()
      })
    },
    async copyLink(activity) {
      copyText(activity.qrCodeURL)
      Toast.success('复制成功')
    },
    setQrCodeURL() {
      this.activities.forEach(item => {
        marketingClient
          .mobilePromoterActivityMyActivityCode({
            body: {
              id: item.id
            }
          })
          .then(([err, result]) => {
            item.qrCodeURL = result.data.url
          })
      })
    }
  }
}
</script>

<style scoped>
.tabBar {
  display: flex;
  justify-content: space-around;
  padding: 12px 16px;
  color: #4e5769ff;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 9;
}
.active {
  color: #e8876a;
  font-weight: 500;
}
.active-style {
  width: 24px;
  height: 3px;
  border-radius: 2px;
  opacity: 1;
  border: 0 solid #979797ff;
  background: #f77234ff;
  position: relative;
  top: 11px;
}
.item {
  padding: 12px 16px;
  border: 1.5px solid #ffffffff;
  border-radius: 8px;
  background: linear-gradient(
    180deg,
    #f8f4f3ff 0%,
    #faf9f7ff 27%,
    #fffefeff 98%
  );
}
::v-deep .van-dialog__footer {
  display: none;
}
</style>
