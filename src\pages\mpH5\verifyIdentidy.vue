<template >
  <div v-if="phone" class="phoneChangeStep1" style="">
    <Field readonly size="large" :value="encipherPhone" label="手机号" />
    <Captcha style="width: 100%" v-model="captcha" label="图形验证码" />
    <Otp
      style="width: 100%"
      v-model="otp"
      :captcha="captcha"
      :phone="phone"
      label="验证码"
    />
    <p style="padding: 0 16px; color: #666">
      若该手机号已无法使用或收不到短信验证码请联系客服 95541
    </p>
    <div style="margin: 16px">
      <Button @click="next" round block type="primary">下一步</Button>
    </div>
  </div>
</template>

<script>
import { Form, Field, Button } from 'vant'
import Otp from './otp.vue'
import Captcha from './captcha.vue'
import makePlatformClient from '../../services/platform/makeClient'
import handleError from '../../helpers/handleErrorH5'
const platformClient = makePlatformClient()

export default {
  components: {
    Form,
    Field,
    Otp,
    Captcha,
    Button
  },
  props: {
    nextStepUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      captcha: {
        answer: '',
        token: ''
      },
      otp: {
        answer: '',
        token: ''
      },
      phone: '',
      encipherPhone: ''
    }
  },
  async created() {
    const [err, r] = await platformClient.merchantPlatformProfile({
      body: {}
    })

    if (err) {
      handleError(err)
      return
    }

    this.phone = r.data.user.cellPhone
    this.encipherPhone = this.handleEncipherPhone(r.data.user.cellPhone)
  },
  methods: {
    next() {
      if (!this.captcha.answer) {
        handleError({ message: '请输入图形验证码' })
        return
      }
      if (!this.otp.token) {
        handleError({ message: '请先获取短信验证码' })
        return
      }
      if (!this.otp.answer) {
        handleError({ message: '请输入手机验证码' })
        return
      }

      this.$emit('next', this.otp)
    },
    handleEncipherPhone(phone) {
      let newPhone = phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
      return newPhone
    }
  }
}
</script>

<style>
</style>