<template>
  <div>
    <div
      style="
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      "
    >
      <h3 style="margin: 100px 0 20px 0">{{ tip }}</h3>
      <div style="position: relative">
        <div id="progressbar"><div id="videoParent"></div></div>
      </div>
    </div>
    <AuthFailDialog @back="back" @retry="retry" ref="authFailDialog" />
    <LivingAuthFailDialog
      @back="back"
      @retry="retry"
      ref="livingAuthFailDialog"
    />
  </div>
</template>

<script>
import FaceIDPERtcVideo from '../../../../assets/sdk/rtcsdk.pe.min.js'

import { Toast } from 'vant'
import AuthFailDialog from './authFailDialog.vue'
import LivingAuthFailDialog from './livingAuthFailDialog.vue'
import makePlatformClient from '../../../../services/platform/makeClient'
import handleSuccess from '../../../../helpers/handleSuccessH5'
import handleError from '../../../../helpers/handleErrorH5'
import store from '../../../../helpers/store'
const platformClient = makePlatformClient()

export default {
  components: {
    AuthFailDialog,
    LivingAuthFailDialog
  },
  data() {
    return {
      videoPlay: null,
      rtcVideo: null,
      progressBar: null,
      useCustomPlay: null,
      tip: '请正对镜头', //提示语
      imageBase64: '',
      token: '',
      bizNo: ''
    }
  },
  computed: {
    name() {
      return store.get('__name__')
    },
    idNo() {
      return store.get('__idCardNo__')
    }
  },
  async mounted() {
    const [err, r] = await platformClient.merchantExternalGetFaceToken({
      body: { name: this.name, idNo: this.idNo }
    })

    if (err) {
      handleError(err)
      return
    }

    this.token = r.data.token
    this.bizNo = r.data.bizNo
    this.useCustomPlay = this.$route.query.customplay === 'true' // 使用自定义播放

    setTimeout(() => {
      this.makeFaceIDPERtcVideo()
      this.makeInitProgressBar()

      this.progressBar.animate(0)
      this.rtcVideo.start()
    }, 1000)
  },
  methods: {
    async retry() {
      const [err, r] = await platformClient.merchantExternalGetFaceToken({
        body: { name: this.name, idNo: this.idNo }
      })

      if (err) {
        handleError(err)
        return
      }

      this.token = r.data.token
      this.bizNo = r.data.bizNo

      this.rtcVideo.destroy()
      this.makeFaceIDPERtcVideo()
      this.progressBar.animate(0)
      this.rtcVideo.start()
    },
    back() {
      this.progressBar.animate(0)
      this.$emit('back')
    },
    makeFaceIDPERtcVideo() {
      this.rtcVideo = new FaceIDPERtcVideo({
        videoWrapper: 'videoParent', // 页面中镶嵌video元素的位置
        width: '100%',
        height: '100%',
        host: window.env.faceIDPERtcVideoUrl,
        // host: "wss://*************:9997",
        // host: 'wss://sztface.jsga.gov.cn:9998',
        // host: 'wss://************:9997',
        token: this.token,

        // bizNo: 'megvii',
        bizNo: this.bizNo,
        // livenessType: 'still',
        livenessType: 'flash',

        // videoPermsTimeout: 26000,
        // webSocketTimeout: 26000,
        // WebRTCTimeout: 26000,

        // 传递color给用户，用户监听color变化，修改页面背景颜色
        flashPreViewListener: (color, status, index, sum, progress) => {
          console.log(
            color,
            status,
            index,
            sum,
            progress,
            '传递color给用户，用户监听color变化，修改页面背景颜色'
          )
          this.progressBar.animate(progress / 100)
          document.body.setAttribute('style', `background: ${color}`)
        },
        stillPreViewListener: (status, progress) => {
          console.log(status, progress, '修改进度条')
          this.progressBar.animate(progress / 100)
        },
        // 提示语回调
        tipListener: (tipCode, tipTxt) => {
          this.tip = tipTxt
          console.log(tipCode, tipTxt, '提示语')
        },
        // 状态的改变，可用于记录埋点
        statusListener: data => {
          // document.getElementById("status").innerText = data;
          console.log(data, 'statusListener')
        },
        // 结束获取认证结果
        resultCallback: async (isSuccess, code, message) => {
          this.progressBar.animate(1)

          if (!isSuccess) {
            this.$refs.authFailDialog.open()
            return
          }

          // 获取base64
          const [err, r] = await platformClient.merchantExternalGetFaceResult({
            body: { status: 'OK', bizNo: this.bizNo }
          })

          if (err) {
            handleError(err)
            return
          }

          if (!r.data.result) {
            this.$refs.livingAuthFailDialog.open('活体认证失败')
            return
          }

          const imageBase64 = r.data.imageBase64

          // 校验三要素
          const [err2, r2] =
            await platformClient.merchantExternalThreeEleIndateCheck({
              body: { imageBase64, idNo: this.idNo, name: this.name }
            })

          if (err2) {
            handleError(err2)
            return
          }

          if (!r2.data.isSuccess) {
            // const message = r2.data.errorRsp? r2.data.errorRsp : '认证失败'
            // handleError(message)
            this.$refs.livingAuthFailDialog.open(r2.data.errorRsp)
            return
          }

          const [err3, r3] = await platformClient.merchantAuthFaceAction({
            body: {
              video: imageBase64,
              name,
              idCardNo: this.idNo
            }
          })

          if (err3) {
            handleError(err3)
            return
          }

          handleSuccess('验证成功！')

          setTimeout(() => {
            this.$router.replace('/workbench')
          }, 1000)
        },
        autoplayFailCallback: this.useCustomPlay
          ? play => {
              this.videoPlay = play
            }
          : undefined
      })
    },
    makeInitProgressBar() {
      this.progressBar = new ProgressBar.Circle('#progressbar', {
        color: '#FCB03C',
        duration: 1000,
        svgStyle: {
          position: 'absolute',
          width: '223px',
          top: -1,
          left: -1
        }
      })
    }
  }
}
</script>
<style scoped>
::v-deep #progressbar {
  width: 223px;
  height: 223px;
}
::v-deep #videoParent {
  width: 100%;
  height: 100%;
}
::v-deep video {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: fill;
}
</style>
