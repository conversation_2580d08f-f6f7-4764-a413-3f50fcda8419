<template>
  <div style="background: #f5f5f5; min-height: 100vh">
    <Search @search="search" style="margin-bottom: 10px" />
    <div style="padding: 0 10px" v-show="contracts.length > 0">
      <Item
        v-for="(contract, index) in contracts"
        :key="index"
        :contract="contract"
        @click.native="goContract(contract.contractId, contract.stepId)"
      ></Item>
    </div>
    <NoData
      v-show="contracts && contracts.length <= 0"
      style="margin: 100px auto; width: 200px"
    />
  </div>
</template>

<script>
import { Toast } from 'vant'
import Item from '../../components/mpH5/contracts/item.vue'
import Search from '../../components/mpH5/contracts/search.vue'
import NoData from '../../components/ui/svgIcon/noData.vue'
import handleErrorH5 from '../../helpers/handleErrorH5'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  name: 'contracts',
  components: {
    Item,
    Search,
    NoData
  },
  data() {
    return {
      contracts: [],
      searchValue: '',
      loading: false
    }
  },
  async created() {
    this.selectContracts()
  },

  methods: {
    async selectContracts(body = {}) {
      Toast({
        message: '加载中...',
        forbidClick: true
      })
      const [err, r] = await platformClient.elContractGetUserAllElContract({
        body
      })

      if (err) {
        handleErrorH5(err)
        return
      }

      this.loading = false
      Toast.clear()
      this.contracts = r.data
    },
    search(val) {
      this.selectContracts({ key: val })
    },
    goContract(id, stepId) {
      this.$router.push({ path: `/contract/${id}/${stepId}` })
    }
  }
}
</script>

<style>
</style>