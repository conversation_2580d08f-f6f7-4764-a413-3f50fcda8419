import autoUpdate from '../helpers/autoUpdate'
import { getToken } from 'kit/helpers/token.js'

const sendLog = (params = {}) => {
  if (!window.location.href.includes('qa')) return
  const token = getToken()
  fetch('https://olading.izhaoxm.cn/api/log', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      source: 'kit',
      token: token,
      ...params
    })
  })
}

export const tokenInterceptor = () => {
  return function (resource, options) {
    const token = getToken()

    sendLog()

    const headers = {
      Authorization: `Bearer ${token}`
    }

    options.headers = { ...options.headers, ...headers }

    return [null, resource, options]
  }
}

export const gatewayInterceptor = gateway => {
  return function (resource, options) {
    resource = `${gateway}${resource}`
    return [null, resource, options]
  }
}

export const requestDefaultHeadersInterceptor = () => {
  return function (resource, options) {
    options.method = options.method || 'POST'
    options.headers['Content-Type'] =
      options.headers['Content-Type'] || 'application/json'

    return [null, resource, options]
  }
}

export const autoUpdateInterceptor = () => {
  return function (resource, options) {
    autoUpdate()

    return [null, resource, options]
  }
}

// var actions = {}
// var needDuplicateSubmissionAPIs = [
//   //模板更新
//   'template/update',
//   //模板保存
//   'template/makeSave',
//   //模板第二步提交
//   'template/makeSubmit',
//   //预览填写
//   'signing/saveDraft',
//   'signing/updateDraft',
//   'signing/saveDraftModifiableData',
//   'signing/submitDraft'
// ]

// setInterval(() => {
//   actions = {}
// }, 7000)

// export const avoidDuplicateSubmissionRequestInterceptor = (
//   resource,
//   options
// ) => {
//   //todo 改成实例形式
//   // var isNeed = false
//   // for (var c of needDuplicateSubmissionAPIs) {
//   //   if (resource.includes(c)) {
//   //     isNeed = true
//   //     break
//   //   }
//   // }
//   // if (!isNeed) {
//   //   return [null, resource, options]
//   // }
//   // if (actions[resource]) {
//   //   return [
//   //     {
//   //       resource: resource,
//   //       options: options,
//   //       message: 'duplicated request'
//   //     },
//   //     null,
//   //     null
//   //   ]
//   // }

//   // actions[resource] = true

//   return [null, resource, options]
// }

////////////////////////Response Interceptors////////////////////////

export const jsonResponseInterceptor = () => {
  return async function (resource, options, result) {
    const contentType = result.headers.get('content-type')
    if (!result || !contentType.includes('application/json')) {
      return [null, result]
    }

    var err = null
    var r = null

    r = await result.json()
    //格式化errorCode
    if (r && typeof r.error !== 'undefined' && parseInt(r.status, 10) !== 0) {
      err = {
        ...r,
        errorCode: parseInt(r.status, 10),
        message: r.error
      }

      r = null
    }

    if (r && typeof r.ok !== 'undefined' && r.ok === false) {
      err = {
        ...r,
        errorCode: parseInt(r.errorCode, 10),
        message: r.message
      }

      r = null
    }

    if (r && r.success === false && parseInt(r.errorCode, 10)) {
      err = {
        ...r,
        errorCode: parseInt(r.errorCode, 10),
        message: r.message
      }

      r = null
    }

    if (err) {
      sendLog({
        apiURL: resource,
        request: options,
        response: err
      })
    }
    return [err, r]
  }
}
