<template>
  <div
    style="width: 319px; position: relative; display: flex; align-items: center"
  >
    <Field
      placeholder="请输入图形验证码"
      clearable
      type="tel"
      @input="changeAnswer"
      maxlength="4"
      :label="label"
      size="small"
      @clear="clearAnswer"
      v-model="answer"
    >
    </Field>
    <img
      style="position: absolute; right: 16px; height: 30px"
      :src="src"
      @click="load()"
      v-if="token"
    />
  </div>
</template>

<script>
import { Field } from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
import makePlatformClient from 'kit/services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  components: {
    Field
  },
  props: {
    label: {
      type: String,
      default: ''
    }
  },
  created() {
    this.load()
  },
  computed: {
    src() {
      const baseUrl = window.env?.platform?.api
      if (!baseUrl) {
        handleError('无法获取平台的API地址')
        return
      }
      return `${baseUrl}captcha?token=${encodeURIComponent(this.token)}`
    }
  },
  data() {
    return {
      token: '',
      answer: ''
    }
  },
  methods: {
    clearAnswer() {
      this.answer = ''
      this.$emit('input', {})
      this.load()
    },
    changeAnswer(v) {
      if (v.length === 4) {
        this.$emit('input', {
          token: this.token,
          answer: this.answer
        })
      }
    },
    async load() {
      const [err, r] = await platformClient.merchantPlatformCreateCaptcha()
      if (err) {
        handleError(err)
        return
      }

      if (!r.data) {
        handleError('验证码获取失败')
        return
      }

      this.token = r.data
    }
  }
}
</script>

<style></style>
