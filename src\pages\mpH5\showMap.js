function showMap(containerID) {
    var map;
    var zoom = 12;
    //初始化地图对象
    map = new T.Map(containerID);
    //设置显示地图的中心点和级别
    var lo = new T.Geolocation();
    function fn (e) {
        if (this.getStatus() == 0){
            map.centerAndZoom(e.lnglat, 15)
            console.log("获取定位坐标："+e.lnglat.lat + "," + e.lnglat.lng)
            var marker = new T.Marker(e.lnglat);
            map.addOverLay(marker);

        }
        if(this.getStatus() == 1){
            map.centerAndZoom(e.lnglat, e.level)
            console.log("获取定位坐标："+e.lnglat.lat + "," + e.lnglat.lng)
            var marker = new T.Marker(e.lnglat);
            map.addOverLay(marker);
        }
    }
    lo.getCurrentPosition(fn);
}
export default showMap