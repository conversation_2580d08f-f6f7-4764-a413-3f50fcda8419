<template>
  <div v-loading="loading">
    <!-- 编辑按钮 -->
    <div style="background: white; padding: 10px;  display: flex; justify-content: flex-end; align-items: center;">
      <div v-if="!isEditMode">
        <el-button type="primary" @click="toggleEditMode" style="background: #4F71FF; border-color: #4F71FF;">
          <i class="el-icon-edit"></i>
          编辑
        </el-button>
      </div>
    </div>

    <div style="padding: 10px;">
      <el-form :model="formData" :rules="rules" ref="form" label-width="120px" style="width: 800px; margin-left: 50px;">
        
        <Title title="基本信息" />

        <!-- 第一行：姓名 和 身份证件号码 -->
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="formData.name" :readonly="!isEditMode" placeholder="请输入姓名" maxlength="20" ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="mobile">
              <el-input v-model="formData.mobile" :readonly="!isEditMode" :disabled="isEditMode" placeholder="请输入手机号"></el-input>
            </el-form-item>
          </el-col>
          
        </el-row>

        <!-- 第二行：手机号 和 年龄 -->
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="身份证件号码" prop="idCard">
              <el-input v-model="formData.idCard" :readonly="!isEditMode" :disabled="isEditMode" placeholder="请输入身份证件号码"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年龄">
              <el-input v-model="formData.age" :readonly="!isEditMode" :disabled="isEditMode" placeholder="年龄"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行：证件有效期 和 性别 -->
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="证件有效期">
              <el-input v-model="formData.idCardPeriod" :readonly="!isEditMode" placeholder="证件有效期"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别">
              <el-input v-model="formData.gender" :readonly="!isEditMode" :disabled="isEditMode" placeholder="性别"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第四行：民族 -->
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="民族">
              <el-input v-model="formData.nation" :readonly="!isEditMode" placeholder="民族"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="详细地址">
              <el-input v-model="formData.householdAddress" :readonly="!isEditMode" placeholder="详细地址"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第五行：银行卡号 -->
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="银行卡号" prop="bankCard">
              <el-input v-model="formData.bankCard" :readonly="!isEditMode" placeholder="请输入银行卡号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户行">
              <el-input v-model="formData.cardBank" :readonly="!isEditMode" placeholder="开户行" maxlength="64" ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 编辑模式下的操作按钮 -->
        <div v-if="isEditMode" style="text-align: center; margin-top: 40px;">
          <el-form-item label-width="0">
            <el-button type="default" @click="cancelEdit">取消</el-button>
            <el-button type="primary" @click="confirmEdit" :loading="submitting">确定</el-button>
          </el-form-item>
        </div>

      </el-form>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import Title from './components/title.vue'

const client = makeClient()

export default {
  components: { Title },
  data() {
    return {
      loading: true,
      isEditMode: false,
      submitting: false,
      laborId: null,
      laborData: {}, // 原始数据
      formData: {}, // 表单数据
      originalFormData: {}, // 用于取消编辑时恢复数据
      rules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { max: 20, message: '姓名不能超过20个字符', trigger: 'blur' }
        ],
        idCard: [
          { required: true, message: '请输入身份证件号码', trigger: 'blur' },
          { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的18位身份证号码', trigger: 'blur' }
        ],
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的11位手机号码', trigger: 'blur' }
        ],
        bankCard: [
          { required: true, message: '请输入银行卡号', trigger: 'blur' },
          { pattern: /^[1-9]\d{11,19}$/, message: '请输入正确的12-20位银行卡号', trigger: 'blur' }
        ],
        cardBank: [
          { max: 64, message: '开户行不能超过64个字符', trigger: 'blur' }
        ]
      }
    }
  },

  async created() {
    // 获取路由参数中的人员ID
    this.laborId = this.$route.params.id
    
    // 加载数据
    await this.loadLaborData()
  },

  methods: {
    // 加载人员数据
    async loadLaborData() {
      try {
        const [err, response] = await client.getLaborDetail({
          method: 'GET',
          pathParams: { id: this.laborId }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response.success) {
          this.laborData = response.data || {}
          this.populateFormData(response.data)
        } else {
          this.$message.error(response.message || '获取人员信息失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.loading = false
      }
    },

    // 填充表单数据
    populateFormData(data) {
      this.formData = {
        name: data.name || '',
        idCard: data.idCard || '',
        mobile: data.mobile || data.cellphone || '',
        age: data.age || '',
        idCardPeriod: data.idCardPeriod || '',
        gender: data.gender || '',
        nation: data.nation || '',
        householdAddress: data.householdAddress || '',
        bankCard: data.bankCard || '',
        cardBank: data.cardBank || ''
      }
      // 保存原始数据用于取消编辑
      this.originalFormData = { ...this.formData }
    },

    // 切换编辑模式
    toggleEditMode() {
      this.isEditMode = true
      // 保存当前数据状态
      this.originalFormData = { ...this.formData }
    },

    // 取消编辑
    cancelEdit() {
      this.isEditMode = false
      // 恢复原始数据
      this.formData = { ...this.originalFormData }
      // 清除表单验证状态
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },

    // 确认编辑
    async confirmEdit() {
      // 表单验证
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }
      try {
        this.submitting = true

        // 调用更新接口，根据SupplierLaborVo字段映射
        const [err, response] = await client.updateLabor({
          body: {
            id: this.laborId,
            ...this.formData
            // name: this.formData.name,
            // idCard: this.formData.idCard,
            // mobile: this.formData.mobile,
            // nation: this.formData.nation,
            // householdAddress: this.formData.householdAddress,
            // bankCard: this.formData.bankCard,
            // cardBank: this.formData.cardBank
          }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response.success) {
          this.$message.success('人员信息更新成功')
          this.isEditMode = false
          // 重新加载数据
          await this.loadLaborData()
        } else {
          this.$message.error(response.message || '更新失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style scoped>
</style>
