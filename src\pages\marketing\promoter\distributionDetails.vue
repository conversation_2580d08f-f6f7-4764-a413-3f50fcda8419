<template>
  <div class="details">
    <div class="bg">
      <!-- <img src="../../../assets/images/detailed-list-bg.png" /> -->
      <div
        style="
          color: #ffffffff;
          font-size: 0.24rem;
          padding: 0.32rem 0 0 0.24rem;
        "
      >
        历史发放&nbsp;(张)
      </div>
      <div style="color: #ffffffff; font-size: 38px; padding: 0.24rem">
        {{ detail.useCount }}
      </div>
      <div
        style="
          display: flex;
          position: absolute;
          left: 0.24rem;
          bottom: 0.24rem;
          color: #ffffffff;
          font-size: 12px;
        "
      >
        <span>共计可发放：{{ detail.amount }}</span>
        <span style="margin: 0 0.1rem">|</span>
        <span>剩余可发放：{{ detail.leftCount }}</span>
      </div>
    </div>
    <div v-if="grantList.length">
      <div class="header">
        <span style="flex: 0 0 120px">领用时间</span>
        <span style="flex: 0 0 105px">姓名</span>
        <span style="flex: 1">领用人id</span>
      </div>

      <div
        style="
          display: flex;
          color: #1e2228ff;
          font-size: 12px;
          margin: 0 12px;
          padding: 12px 16px;
          margin: 0 12px;
          align-items: center;
          box-sizing: border-box;
        "
        v-for="(item, index) in grantList"
        :key="index"
        :class="[
          index % 2 == 0 ? 'background' : 'noBackground',
          index == grantList.length - 1 ? 'last-borderRadius' : ''
        ]"
      >
        <span style="flex: 0 0 120px">{{ item.getTime }}</span>
        <span
          style="
            flex: 0 0 100px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-right: 5px;
          "
          >{{ item.name }}</span
        >
        <span style="flex: 1; word-break: break-all">
          {{ item.openid }}
        </span>
      </div>
    </div>
    <div
      v-if="!grantList.length"
      style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-top: 120px;
      "
    >
      <img width="120px" src="../../../assets/images/no-data.svg" />
      <span style="color: #828b9bff; font-size: 14px">暂无发放明细</span>
    </div>
  </div>
</template>
<script>
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()
import handleErrorH5 from 'kit/helpers/handleErrorH5'

export default {
  data() {
    return {
      detail: {},
      grantList: []
    }
  },
  created() {
    this.getGrantList()
  },
  methods: {
    async getGrantList() {
      const [err, r] =
        await marketingClient.mobilePromoterActivityMyActivityGetList({
          body: {
            id: this.$route.query.id
          }
        })
      if (err) {
        handleErrorH5(err)
        return
      }

      r.data.getDetailVoList.forEach(item => {
        item.getTime =
          item.getTime.split('-')[1] + '-' + item.getTime.split('-')[2]
      })

      r.data.getDetailVoList.forEach(item => {
        if (!item.name) return (item.name = '-')
      })
      this.grantList = r.data.getDetailVoList
      this.detail = r.data
    }
  }
}
</script>
<style scoped>
.details {
  min-height: 100vh;
  box-sizing: border-box;
  padding-top: 0.26rem;
  background: url('../../../assets/images/list_bg.png');
  background-size: cover;
}
.bg {
  position: relative;
  background-image: url('kit/assets/images/detailed-list-bg.png');
  background-size: cover;
  margin: 0 0.24rem 0.2rem;
  height: 2.76rem;
}
.header {
  background: #ffffff;
  display: flex;
  color: #828b9bff;
  font-size: 12px;
  margin: 0 12px;
  padding: 12px 0 12px 16px;
  border-radius: 8px 8px 0 0;
  position: sticky;
  top: 0;
}
.background {
  background: #f8f9fd;
}
.noBackground {
  background: #ffffff;
}
.last-borderRadius {
  border-radius: 0 0 8px 8px;
}
</style>
