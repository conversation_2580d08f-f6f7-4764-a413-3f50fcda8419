<template>
  <div style="padding-bottom: 80px; min-height: 600px">
    <Images :imgs="contract.contractPdfPicture" />
    <div
      @click="goContractDetail"
      style="
        right: -14px;
        position: fixed;
        top: 75px;
        border: 1px solid #4f71ff;
        color: #4f71ff;
        border-right: none;
        border-radius: 20px;
        padding-left: 15px;
        width: 49px;
        height: 23px;
        display: inline-block;
        background: #fff;
      "
    >
      <span>详情</span>
    </div>
  </div>
</template>

<script>
import { Button } from 'vant'
import Images from '../../components/mpH5/contract/images.vue'
import handleErrorH5 from '../../helpers/handleErrorH5'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  name: 'contractSign',
  components: {
    Images
  },
  data() {
    return {
      contract: {}
    }
  },
  async mounted() {
    const contractId = this.$route.params.id
    const [err, r] = await platformClient.oladingContractQuery({
      body: {
        contractId,
        customerType: 'NATURAL_PERSON'
      }
    })

    if (err) {
      handleErrorH5(err)
      return
    }

    this.contract = r.data
  },
  methods: {
    goContractDetail() {
      this.$router.push({ path: '/contractDetail/' + this.contract.id })
    }
  }
}
</script>

<style>
</style>