<template>
  <el-dialog
    title="退回发票"
    :visible.sync="dialogVisible"
    width="500px"
    @close="handleClose"
  >
    <p style="margin-bottom: 10px">确定退回该发票吗？</p>
    <el-form :model="form" :rules="rules" ref="form">
      <el-form-item prop="reason">
        <el-input
          type="textarea"
          v-model="form.reason"
          placeholder="请输入退回原因"
          maxlength="200"
          show-word-limit
          :rows="4"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import handleError from 'kit/helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    invoiceId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      dialogVisible: this.visible,
      form: {
        reason: ''
      },
      rules: {
        reason: [
          { required: true, message: '请输入退回原因', trigger: 'blur' },
          { max: 200, message: '原因不超过200字', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.form.reason = ''
        this.$nextTick(() => {
          this.$refs.form.clearValidate()
        })
      }
    }
  },
  methods: {
    handleClose() {
      if (this.loading) return
      this.$emit('close')
    },
    handleConfirm() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          this.loading = true
          try {
            const [err, res] = await client.supplierInvoicesReturn({
              body: {
                invoiceId: this.invoiceId,
                reason: this.form.reason
              }
            })

            if (err) {
              handleError(err)
              return
            }

            this.$message.success(res.message || '退回成功')
            this.$emit('success')
            this.handleClose()
          } finally {
            this.loading = false
          }
        }
      })
    }
  }
}
</script>
