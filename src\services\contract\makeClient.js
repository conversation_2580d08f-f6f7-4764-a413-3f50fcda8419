import {
  tokenInterceptor,
  autoUpdateInterceptor,
  requestDefaultHeadersInterceptor,
  gatewayInterceptor,
  jsonResponseInterceptor
} from '../interceptors'
import store from '../../helpers/store'
import HttpClient from '../httpClient'
import Client from './client'

const httpClient = new HttpClient()

const token = store.get('token')

httpClient.attachGlobalRequestInterceptor(autoUpdateInterceptor())
httpClient.attachGlobalRequestInterceptor(tokenInterceptor(token))
httpClient.attachGlobalRequestInterceptor(requestDefaultHeadersInterceptor())
httpClient.attachGlobalRequestInterceptor((resource, options) => {
  resource = resource.substr(4)
  resource = `${window.env.api}/contract2${resource}`
  return [null, resource, options]
})
httpClient.attachGlobalResponseInterceptor(jsonResponseInterceptor())

const client = new Client(httpClient)

const makeClient = () => client

export default makeClient
