<template>
  <div
    class="login"
    ref="login"
    style="height: 100vh; text-align: center; overflow: hidden"
  >
    <div ref="box" style="position: relative">
      <div style="padding: 60px 30px">
        <h1 style="font-size: 20px; margin-bottom: 24px">数字营销推广系统</h1>
        <div
          style="
            width: 56px;
            height: 56px;
            margin: 0 auto 40px;
            border-radius: 8px;
            opacity: 1;
            font-size: 20px;
            color: #ffffffff;
            line-height: 56px;
          "
        >
          <img
            style="width: 100%"
            src="kit/assets/images/marketing-promoter-logo.png"
            alt=""
          />
        </div>
        <Form ref="form" :model="form">
          <div class="row-input" style="margin-bottom: 16px">
            <i
              class="icon iconfont icon-application-phone"
              style="font-size: 24px; color: #828b9b"
            ></i>
            <Field
              class="mobile"
              placeholder="请输入手机号"
              maxlength="11"
              :rules="rules.phone"
              type="tel"
              v-model="form.phone"
              clearable
            />
          </div>
          <div class="row-input" style="margin-bottom: 16px">
            <i
              class="icon iconfont icon-base-img"
              style="font-size: 24px; color: #828b9b"
            ></i>
            <Captcha v-model="captcha" :rules="rules.captcha" />
          </div>
          <div class="row-input">
            <i
              class="icon iconfont icon-base-privacy"
              style="font-size: 24px; color: #828b9b"
            ></i>
            <Otp
              v-model="form.code"
              maxlength="6"
              @otp="onOtp"
              :captcha="captcha"
              :phone="form.phone"
              :rules="rules.code"
            />
          </div>
        </Form>

        <Button
          :disabled="disabled"
          :loading="loading"
          style="width: 100%; margin-top: 30px; border-radius: 6px"
          @click="login"
          type="primary"
          size="middle"
        >
          推广员登录
        </Button>
        <div style="display: flex; justify-content: center; margin-top: 20px">
          <Checkbox shape="square" v-model="agreed">已阅读并同意</Checkbox>
          <a
            style="color: #f77234"
            @click="$router.push('userAgreementAndTerms')"
            >《用户协议》</a
          >
          及<a style="color: #f77234" @click="$router.push('privacyPolicy')"
            >《隐私政策》</a
          >
        </div>
      </div>

      <img
        style="position: absolute; left: 1px; bottom: 0; width: 100%"
        src="../../../assets/images/log-on-bg.png"
      />
    </div>
  </div>
</template>

<script>
import { Field, Button, Toast, Form, Checkbox } from 'vant'
import Captcha from './loginCaptcha.vue'
import Otp from './loginOtp.vue'
import handleError from 'kit/helpers/handleErrorH5'
import { setToken } from 'kit/helpers/token'
import store from 'kit/helpers/store'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()
import makePlatformClient from 'kit/services/platform/makeClient'
const platformClient = makePlatformClient()
import * as reg from 'kit/helpers/regexp'

const cacheLoginMobile = 'promoterH5LoginMobile'

export default {
  mounted() {
    this.$refs.box.style.height = `${this.$refs.login.clientHeight}px`
  },
  async beforeRouteEnter(to, from, next) {
    const token = store.get('token')
    if (token) {
      return next('/activities')
    }
    next()
  },

  components: {
    Field,
    Captcha,
    Otp,
    Button,
    Form,
    Checkbox
  },
  computed: {
    disabled() {
      const { form, otp } = this
      if (!form.phone) {
        return true
      }
      if (!otp.answer) {
        return true
      }
      if (!this.agreed) {
        return true
      }
      return false
    }
  },
  data() {
    return {
      configInfo: {},
      form: {
        phone: store.get(cacheLoginMobile),
        code: ''
      },
      captcha: {
        answer: '',
        token: ''
      },
      otp: {
        answer: '',
        token: ''
      },
      logo: '',
      loading: false,
      agreed: false,
      rules: {
        phone: [
          { required: true },
          {
            pattern: reg.PHONE_NUMBER_REGEX,
            message: '请输入有效的手机号'
          }
        ],
        captcha: [
          { required: true },
          {
            pattern: reg.VERIFICATION_IMG_CODE_REGEX,
            message: '验证码必须为4位数字'
          }
        ],
        code: [
          { required: true },
          {
            pattern: reg.VERIFICATION_SMS_CODE_REGEX,
            message: '验证码必须为6位数字'
          }
        ]
      }
    }
  },
  async created() {
    const [err, r] = await platformClient.merchantAppletsConfigGetConfigInfo()
    if (err) {
      handleError(err)
      return
    }

    this.configInfo = r.data
  },
  methods: {
    onOtp(otp) {
      this.otp = {
        ...otp
      }
    },
    async login() {
      await this.$refs.form.validate()

      if (!this.otp.token) return Toast.fail('请先获取短信验证码')

      this.loading = true

      const [err, r] = await marketingClient.mobilePromoterSmsLogin({
        body: {
          mobile: this.form.phone,
          smsCode: this.otp.answer,
          smsToken: this.otp.token
        }
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      store.set(cacheLoginMobile, this.form.phone)

      setToken(r.data.token)

      this.$router.replace('/activities')
    }
  }
}
</script>

<style scoped>
.row-input {
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
}
::v-deep .van-cell {
  padding: 8px 0 8px 8px;
}
::v-deep .van-field__body {
  height: 24px;
  line-height: 24px;
}
::v-deep .van-cell::after {
  display: none !important;
}
::v-deep .van-field__control {
  font-size: 16px;
}
::v-deep .van-checkbox__icon .van-icon {
  border-radius: 4px;
}
</style>
