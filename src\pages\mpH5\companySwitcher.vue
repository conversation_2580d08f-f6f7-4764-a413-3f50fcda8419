<template>
  <Popup class="companySwticher" v-model="shown" position="bottom">
    <Picker
      title="切换企业"
      @confirm="confirm"
      @cancel="close()"
      :columns="merchantNames"
      show-toolbar
    ></Picker>
  </Popup>
</template>

<script>
import { Picker, Popup } from 'vant'
import store from '../../helpers/store'
import handleError from '../../helpers/handleErrorH5'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()
export default {
  components: {
    Picker,
    Popup
  },
  computed: {
    merchantNames() {
      var r = []
      for (var c of this.joinedMerchant) {
        r.push({
          text: c.merchant.name,
          merchantId: c.merchantId,
          disabled: c.merchantId === this.currentMerchantId
        })
      }

      return r
    }
  },
  props: {
    joinedMerchant: Array,
    currentMerchantId: Number
  },
  data() {
    return {
      shown: false
    }
  },
  methods: {
    async confirm(c) {
      //renew token
      const [err, r] = await platformClient.merchantPlatformRenewToken({
        body: { merchantId: c.merchantId }
      })
      if (err) {
        handleError(err)
        return
      }

      store.set('token', r.data.token)

      window.location.reload()
      //refresh page
      this.close()
    },
    open() {
      this.shown = true
    },
    close() {
      this.shown = false
    }
  }
}
</script>
}
<style></style>
