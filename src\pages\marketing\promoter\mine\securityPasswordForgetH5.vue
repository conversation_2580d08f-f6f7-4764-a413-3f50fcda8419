<template>
  <div
    class="securityPasswordForgetChange"
    style="min-height: 100vh; background: #f2f3f7; padding: 13px 12px 0"
  >
    <VerifyIdentidy @next="next" v-if="step === 0" />
    <template v-else>
      <Field
        v-model="newPassword1"
        type="password"
        maxlength="6"
        label="新安全密码"
        placeholder="请输入新安全密码"
      />
      <Field
        v-model="newPassword2"
        type="password"
        maxlength="6"
        label="确认安全密码"
        placeholder="请再次输入新安全密码"
      />
      <Button @click="updateSecurityPassword" block round type="primary"
        >确定</Button
      >
    </template>
  </div>
</template>

<script>
import { Field, Button } from 'vant'
import VerifyIdentidy from './verifyIdentidyH5.vue'
import makePlatformClient from 'kit/services/platform/makeClient'
import handleError from 'kit/helpers/handleErrorH5'
import handleSuccess from 'kit/helpers/handleSuccessH5'
const platformClient = makePlatformClient()
export default {
  components: {
    Field,
    Button,
    VerifyIdentidy
  },
  data() {
    return {
      newPassword1: '',
      newPassword2: '',
      passwordType: 'SAFE',
      otp: {
        answer: '',
        token: ''
      },
      step: 0
    }
  },
  methods: {
    async updateSecurityPassword() {
      if (this.newPassword1 !== this.newPassword2) {
        handleError({ message: '两次密码输入不一致' })
        return
      }

      const body = {
        challenge: this.otp.answer,
        mode: 'SMS',
        otpToken: this.otp.token,
        password: this.newPassword1,
        type: 'SECURITY'
      }

      const [err, r] =
        await platformClient.merchantAccountSecurityPasswordUpdate({ body })
      if (err) {
        handleError(err)
      }

      handleSuccess('更改成功')
      this.$router.go(-1)
    },
    next(otp) {
      this.step = 1
      this.otp = otp
    }
  }
}
</script>

<style></style>
