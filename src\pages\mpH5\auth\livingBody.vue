<template>
  <div class="livingBody">
    <LivingBodyStep1H5 @startVerification="next" v-if="step===0"/>
    <LivingBodyStep2H5 @back="back" v-if="step===1"/>
  </div>
</template>

<script>
import LivingBodyStep1H5 from './livingBody/livingBodyStep1.vue'
import LivingBodyStep2H5 from './livingBody/livingBodyStep2.vue'
export default {
  components:{
    LivingBodyStep1H5,
    LivingBodyStep2H5
  },
  data(){
    return{
      step:0
    }
  },
  methods:{
    next(){
      this.step = 1
    },
    back(){
      this.step = 0
    }
  }
}
</script>

<style>

</style>