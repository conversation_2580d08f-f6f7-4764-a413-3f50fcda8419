<template>
  <div class="contract" style="padding-bottom: 80px; min-height: 600px">
    <Images :imgs="contract.archiveUrl" />
    <div
      style="
        position: fixed;
        width: 100%;
        bottom: 0;
        height: 80px;
        background: #fff;
      "
    >
      <Button
        @click="downloadContract"
        style="width: 300px; margin: 0 auto"
        round
        block
        type="primary"
      >
        下载文件
      </Button>
    </div>
    <div
      @click="goContractDetail"
      style="
        right: -14px;
        position: fixed;
        top: 75px;
        border: 1px solid #4f71ff;
        color: #4f71ff;
        border-right: none;
        border-radius: 20px;
        padding-left: 15px;
        width: 49px;
        height: 23px;
        display: inline-block;
        background: #fff;
      "
    >
      <span>详情</span>
    </div>
    <DownloadDialog ref="downloadDialog" @sendEmail="sendEmail" @copy="copy" />
    <BindEmailDialog ref="bindEmailDialog" @submit="bindEmail" />
  </div>
</template>

<script>
import { Button } from 'vant'
import Images from '../../components/mpH5/contract/images.vue'
import BindEmailDialog from '../../components/mpH5/contract/bindEmailDialog.vue'
import DownloadDialog from '../../components/mpH5/contract/downloadDialog.vue'
import { ContractStatusSignCompleted } from '../../formatters/mpH5/constants'
import handleErrorH5 from '../../helpers/handleErrorH5'
import handleSuccess from '../../helpers/handleSuccessH5'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  name: 'contract',
  components: {
    Images,
    Button,
    DownloadDialog,
    BindEmailDialog
  },
  data() {
    return {
      contract: {},
      ContractStatusSignCompleted,
      email: ''
    }
  },
  async created() {
    const contractId = this.$route.params.id
    const stepId = this.$route.params.stepId
    const [err, r] = await platformClient.elContractGetContractDetail({
      body: {
        contractId,
        stepId
      }
    })

    if (err) {
      handleErrorH5(err)
      return
    }

    this.contract = r.data

    const [err1, r1] = await platformClient.merchantPlatformProfile({
      body: {}
    })

    if (err1) {
      handleErrorH5(err)
      return
    }

    this.email ='' 
  },
  methods: {
    async downloadContract() {
      this.$refs.downloadDialog.open()
    },
    goContractDetail() {
      const id = this.$route.params.id
      const stepId = this.$route.params.stepId
      this.$router.push({ path: `/contractDetail/${id}/${stepId}` })
    },
    bindEmail(email) {
      this.email = email
      this._sendEmail()
    },
    sendEmail() {
      if (!this.email) {
        this.$refs.bindEmailDialog.open()
        return
      }

      this._sendEmail()
    },
    async _sendEmail() {
      const [err, r] = await platformClient.merchantSendContractMail({
        body: {
          contractId: this.contract.contractId,
          emailTo: this.email
        }
      })

      if (err) {
        handleErrorH5(err)
        return
      }

      handleSuccess('发送成功')
      this.$refs.downloadDialog.close()
    },
    copy() {
      this._copy(this.contract.archiveUrl)
      this.$refs.downloadDialog.close()
    },
    _copy(text) {
      // 数字没有 .length 不能执行selectText 需要转化成字符串
      const textString = encodeURI(text.toString())
      let input = document.querySelector('#copy-input')
      if (!input) {
        input = document.createElement('input')
        input.id = 'copy-input'
        input.readOnly = 'readOnly' // 防止ios聚焦触发键盘事件
        input.style.position = 'absolute'
        input.style.left = '-1000px'
        input.style.zIndex = '-1000'
        document.body.appendChild(input)
      }

      input.value = textString
      // ios必须先选中文字且不支持 input.select();
      selectText(input, 0, textString.length)
      console.log(document.execCommand('copy'), 'execCommand')
      if (document.execCommand('copy')) {
        document.execCommand('copy')
        alert('已复制到粘贴板')
      }
      input.blur()

      // input自带的select()方法在苹果端无法进行选择，所以需要自己去写一个类似的方法
      // 选择文本。createTextRange(setSelectionRange)是input方法
      function selectText(textbox, startIndex, stopIndex) {
        if (textbox.createTextRange) {
          //ie
          const range = textbox.createTextRange()
          range.collapse(true)
          range.moveStart('character', startIndex) //起始光标
          range.moveEnd('character', stopIndex - startIndex) //结束光标
          range.select() //不兼容苹果
        } else {
          //firefox/chrome
          textbox.setSelectionRange(startIndex, stopIndex)
          textbox.focus()
        }
      }
    }
  }
}
</script>

<style>
</style>