<template>
  <div
    style="
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      flex: 1;
    "
  >
    <Field
      placeholder="请输入验证码"
      clearable
      maxlength="6"
      size="large"
      :label="label"
      type="tel"
      @input="changeAnswer"
      @clear="clearAnswer"
      :rules="rules"
      :value="value"
    >
      <template #button>
        <Button
          color="#F77234"
          class="sms-button"
          size="small"
          type="text"
          plain
          :loading="isLoading"
          @click="send"
          v-show="!countdown"
        >
          {{ isFirstTimeSendCode ? '获取验证码' : '重新获取' }}
        </Button>
        <div v-show="countdown">
          <Countdown
            style="color: #a6aebd"
            template="%ds后重新获取"
            ref="countdown"
            @finish="countdown = false"
          />
        </div>
      </template>
    </Field>
  </div>
</template>

<script>
import { Field, Button } from 'vant'
import Countdown from 'kit/components/ui/countdown.vue'
import handleError from 'kit/helpers/handleErrorH5'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

export default {
  props: {
    value: {
      type: String,
      default: ''
    },
    phone: {
      type: String,
      default: ''
    },
    label: {
      tye: String,
      default: ''
    },
    captcha: {
      type: Object,
      default() {
        return {
          answer: '',
          token: ''
        }
      }
    },
    rules: Array
  },
  components: {
    Field,
    Button,
    Countdown
  },
  data() {
    return {
      countdown: false,
      isFirstTimeSendCode: true,
      otp: {
        answer: '',
        token: ''
      },
      isLoading: false
    }
  },
  methods: {
    async send() {
      if (!this.phone) {
        handleError('请输入手机号')
        return
      }
      if (!this.captcha.token || !this.captcha.answer) {
        handleError('请先完成图形验证码')
        return
      }

      this.isLoading = true
      await this._send()
    },
    async _send() {
      const [err, r] = await marketingClient.smsSendSmsCode({
        body: {
          mobile: this.phone,
          captcha: this.captcha.answer,
          captchaToken: this.captcha.token
        }
      })

      this.isLoading = false

      if (err) {
        handleError(err)
        return
      }

      this.countdown = true
      this.$refs.countdown.start()

      this.otp.token = r.data.smsToken
      // this.changeAnswer(this.otp.answer)
    },
    changeAnswer(v) {
      this.$emit('input', v)
      this.otp.answer = v
      this.$emit('otp', this.otp)
    },
    clearAnswer() {
      this.answer = ''
      this.$emit('input', '')
      this.$emit('otp', {
        answer: '',
        token: ''
      })
    }
  }
}
</script>

<style scoped>
::v-deep .sms-button {
  font-size: 14px;
  font-weight: 400;
  left: 8px;
  position: relative;
}
</style>
