import goApproval from './goApproval'

// 提交补卡
const fixAttend = (attendGroup, checkInRecord) => {
  if (
    checkInRecord.fixAttendStatus == 'INIT' ||
    checkInRecord.fixAttendStatus == 'SUCCESS'
  ) {
    goApproval({ processId: checkInRecord.approveId })
    return
  }

  if (!attendGroup.hadApprovals()) {
    return '无补卡流程'
  }

  //提交补卡
  const approval = attendGroup.getApprovalByType('FIX_ATTENDANCE')
  const { workDate, standardTime, id } = checkInRecord
  const initProps = {
    '__sys.att.fix_time_slot__': `${workDate}#${standardTime}#${id}`
  }

  const initTableData = encodeURIComponent(JSON.stringify(initProps))
  const tmp = approval.defaultProcess.split('_')
  const receiptId = tmp[0]
  const designId = tmp[1]
  const processDesignId = tmp[2]
  goApproval({
    receiptId,
    designId,
    processDesignId,
    initTableData
  })
}

export default fixAttend
