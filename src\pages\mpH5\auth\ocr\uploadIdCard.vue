<template>
  <Uploader
    style="overflow: hidden"
    :max-count="1"
    :before-read="beforeRead"
    :after-read="afterRead"
  >
    <img
      style="width: 100%; height: 100%"
      v-if="base64Url"
      :src="base64Url"
      alt=""
    />
    <template v-else>
      <img style="height: 100%; width: 100%" :src="uploadBg" alt="" />
      <div
        style="
          position: absolute;
          top: 50%;
          transform: translate(-50%, -50%);
          left: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
        "
      >
        <div
          style="
            height: 45px;
            width: 45px;
            background-color: var(--o-primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
          "
        >
          <span
            style="color: #fff; font-size: 22px"
            class="iconfont icon-base-camera"
          ></span>
        </div>
        <div
          style="
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 13px;
            color: #24262a;
            text-align: center;
            line-height: 13px;
          "
        >
          {{ uploadStr }}
        </div>
      </div>
    </template>
  </Uploader>
</template>

<script>
import { Uploader } from 'vant'
import idCardBackBg from '../../../../assets/images/id_card_back_bg.png'
import idCardFrontBg from '../../../../assets/images/id_card_front_bg.png'
export default {
  components: {
    Uploader
  },
  computed: {
    uploadStr() {
      return this.direction === 'front' ? '上传人像面照片' : '上传国徽面照片'
    },
    uploadBg() {
      return this.direction === 'front' ? idCardFrontBg : idCardBackBg
    }
  },
  props: {
    direction: {
      type: String,
      default: 'front'
    }
  },
  data() {
    return {
      idCardBackBg,
      idCardFrontBg,
      base64Url: ''
    }
  },
  methods: {
    beforeRead(file) {
      console.log(file, 'jadlfjsdklfjlkadsjflk')

      return true
    },
    afterRead(file) {
      console.log(file, 'after')
      this.base64Url = file.content

      this.$emit('uploadSuccess',{direction:this.direction,base64Url:this.base64Url})
    }
  }
}
</script>

<style>
.van-uploader__wrapper,.van-uploader__input-wrapper{
  width: 100%;
  height: 100%;
}
</style>