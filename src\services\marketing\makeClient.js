import {
  autoUpdateInterceptor,
  requestDefaultHeadersInterceptor,
  jsonResponseInterceptor
} from '../interceptors'
import HttpClient from 'kit/services/httpClient'
import Client from './client'
import { getToken } from 'kit/helpers/token'

const httpClient = new HttpClient()

const tokenInterceptor = () => {
  return function (resource, options) {
    const token = getToken()
    const headers = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`
    }

    if (import.meta.env.NODE_ENV === 'test') {
      options.credentials = 'include'
    }

    options.headers = { ...headers, ...options.headers }
    return [null, resource, options]
  }
}

httpClient.attachGlobalRequestInterceptor(autoUpdateInterceptor())
httpClient.attachGlobalRequestInterceptor(tokenInterceptor())
httpClient.attachGlobalRequestInterceptor(requestDefaultHeadersInterceptor())
httpClient.attachGlobalRequestInterceptor((resource, options) => {
  resource = resource.substr(4)
  resource = `${window.env.api}/marketing${resource}`
  if (options.method === 'GET' && options.params) {
    resource += `?${options.params}`
  }
  return [null, resource, options]
})

export const jsonResponseInterceptorApiUploadLog = () => {
  return async function (resource, options, result) {
    try {
      const image = new Image()
      const log = {
        api: resource,
        token: options.headers.Authorization,
        params: options.body || options.params,
        result,
        ua: navigator.userAgent
      }
      // image.src = `https://olading.izhaoxm.cn/api/upload/log?v=${encodeURIComponent(
      //   JSON.stringify(log)
      // )}`
    } catch (err) {
      console.log(err)
    }
    return [null, result]
  }
}

httpClient.attachGlobalResponseInterceptor(jsonResponseInterceptor())
httpClient.attachGlobalResponseInterceptor(
  jsonResponseInterceptorApiUploadLog()
)

const client = new Client(httpClient)

const makeClient = () => client

export default makeClient
