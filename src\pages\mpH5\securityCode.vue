<template>
  <div>
    <div class="securityCode" v-if="showKeyboard">
      <!-- 密码输入框 -->
      <div
        style="
          position: fixed;
          top: 0;
          left: 0;
          height: 100vh;
          width: 100vw;
          background-color: rgba(0, 0, 0, 0.2);
        "
      >
        <div
          style="
            margin: 40% 20px 0 20px;
            background: #fff;
            padding: 20px;
            border-radius: 20px;
            position: relative;
          "
        >
          <i
            @click="showKeyboard = false"
            style="position: absolute; top: 16px; right: 20px"
            class="iconfont icon-remind-close-circle"
          ></i>
          <h3 style="text-align: center">请输入安全密码</h3>
          <PasswordInput
            :value="value"
            :focused="showKeyboard"
            length="6"
            info="密码为 6 位数字"
            @focus="showKeyboard = true"
          />
          <div
            @click="forgotPassword"
            style="text-align: right; color: var(--o-primary-color)"
          >
            忘记密码？
          </div>
        </div>
        <!-- 数字键盘 -->
        <NumberKeyboard
          style="position: fixed; z-index: 9999"
          v-model="value"
          :show="showKeyboard"
          :hide-on-click-outside="false"
          @blur="showKeyboard = false"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { PasswordInput, NumberKeyboard, Popup, Toast } from 'vant'
import makePlatformClient from '../../services/platform/makeClient'
import handleError from '../../helpers/handleErrorH5'
import handleSuccess from '../../helpers/handleSuccessH5'
const platformClient = makePlatformClient()

export default {
  components: {
    PasswordInput,
    NumberKeyboard,
    Popup
  },
  data() {
    return {
      value: '',
      showKeyboard: false,
      verifySuccessGoUrl: ''
    }
  },
  methods: {
    open(url) {
      this.showKeyboard = true
      this.showKeyboard = true
      this.verifySuccessGoUrl = url
    },
    close() {
      this.showKeyboard = false
    },
    forgotPassword() {
      this.$router.push({
        path: '/securityPasswordForgetChange'
      })
    },
    async verifySecurityCode(n) {
      const toast1 = Toast.loading({
        message: '加载中...',
        forbidClick: true
      })

      const [err, r] = await platformClient.merchantUserSecurityVerify({
        body: {
          password: n,
          verifyType: 'SAFE_PASSWORD'
        }
      })

      if (err) {
        handleError(err)
        this.value = ''
        return
      }

      handleSuccess('验证成功')
      this.close()
      this.$emit('verifySuccess')

      if (this.verifySuccessGoUrl) {
        this.$router.push(this.verifySuccessGoUrl)
      }
    },
    clearToast() {
      setTimeout(() => {
        Toast.clear()
      }, 1000)
    }
  },
  watch: {
    value(n) {
      if (!n) {
        return
      }

      if (n.length === 6) {
        this.verifySecurityCode(n)
      }
    }
  }
}
</script>

<style>
</style>