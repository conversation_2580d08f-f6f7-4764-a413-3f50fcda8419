  <template>
  <div class="picker">
    <Field
      @click="showPicker = true"
      :value="showTextTime"
      :label="title"
      :placeholder="placeholder"
      readonly
    />
    <Popup v-model="showPicker" round position="bottom">
      <DatetimePicker
        :title="title"
        :value="datetime"
        type="date"
        show-toolbar
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onConfirm"
        @cancel="onCancel"
      />
    </Popup>
  </div>
</template>

<script>
import { Popup, Field, DatetimePicker } from 'vant'
import dateToString from '../../../../formatters/dateTime'

export default {
  components: {
    Field,
    DatetimePicker,
    Popup
  },
  computed: {
    datetime() {
      if (!this.value) {
        return null
      }

      return new Date(this.value)
    },
    showTextTime() {
      if (!this.value) {
        return null
      }

      if (!this.value instanceof Date) {
        return this.value
      }

      console.log(this.value)

      return dateToString('yyyy/MM/dd', this.value)
    }
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    columns: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: ''
    },
    value: {
      type: String | Date,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      showPicker: false,
      minDate: new Date(1900, 0, 1),
      maxDate: new Date(2098, 10, 1)
    }
  },
  methods: {
    onConfirm(value, index) {
      this.$emit('confirm', value)
      this.showPicker = false
    },
    onCancel() {
      this.showPicker = false
    }
  }
}
</script>

<style>
</style>