<template>
  <div
    class="payroll-detail-container"
    v-loading="loading"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <!-- 摘要信息 -->
    <div
      class="summary-container"
      style="flex: 0 0 auto; padding: 20px; background: var(--o-primary-bg-color); border-radius: 5px; margin-bottom: 20px; font-size: 14px; color: #606266;"
    >
      <el-row :gutter="40">
        <el-col :span="8">
          <div class="summary-item" style="line-height: 32px;">
            <label style="display: inline-block; width: 120px; text-align: right; color: #909399; margin-right: 8px;">客户：</label>
            <span>{{ summaryData.customerName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-item" style="line-height: 32px;">
            <label style="display: inline-block; width: 120px; text-align: right; color: #909399; margin-right: 8px;">服务合同：</label>
            <span>{{ summaryData.contractName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-item" style="line-height: 32px;">
            <label style="display: inline-block; width: 120px; text-align: right; color: #909399; margin-right: 8px;">作业主体：</label>
            <span>{{ summaryData.supplierCorporationName }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="8">
          <div class="summary-item" style="line-height: 32px;">
            <label style="display: inline-block; width: 120px; text-align: right; color: #909399; margin-right: 8px;">税款所属期：</label>
            <span>{{ summaryData.taxPeriod }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-item" style="line-height: 32px;">
            <label style="display: inline-block; width: 120px; text-align: right; color: #909399; margin-right: 8px;">总人数：</label>
            <span>{{ summaryData.totalPeople }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-item" style="line-height: 32px;">
            <label style="display: inline-block; width: 120px; text-align: right; color: #909399; margin-right: 8px;">总应发金额：</label>
            <span>{{ summaryData.totalPayable }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="8">
          <div class="summary-item" style="line-height: 32px;">
            <label style="display: inline-block; width: 120px; text-align: right; color: #909399; margin-right: 8px;">总实发金额：</label>
            <span>{{ summaryData.netPaymentTotal }}</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <div v-if="overStaffList.length > 0" class="over-staff-warning" style="margin-bottom: 20px; padding: 8px; background: #fff7e6; border: 1px solid #ffd591; border-radius: 4px; color: #d48806;">
      <i class="el-icon-warning" style="margin-right: 8px; font-size: 16px;"></i>
      <span style="font-weight: 300;">
        提示！检测到 <strong style="color: #d4380d;">{{ overStaffList.length }}</strong> 名人员平台月累计收入达到十万，存在补缴增值税风险：
      </span>
      <el-button type="text" style="color: #1890ff; padding: 0; font-weight: 300;" @click="showOverStaffDialog = true">
        查看详情
      </el-button>
    </div>

    <!-- 工资明细列表 -->
    <el-table
      :data="tableData"
      size="small"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column prop="name" label="姓名" width="120"></el-table-column>
      <el-table-column
        prop="idCard"
        label="身份证"
        width="180"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="phoneNumber"
        label="手机号"
        width="140"
      ></el-table-column>
      <el-table-column
        prop="payableAmount"
        label="应发金额"
        width="140"
      ></el-table-column>
      <el-table-column
        prop="netPayment"
        label="实发金额"
        width="140"
      ></el-table-column>
      <el-table-column
        prop="accumulatedIncome"
        label="累计收入"
        width="140"
      ></el-table-column>
      <el-table-column
        prop="accumulatedTaxAmount"
        label="累计应纳税额"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="accumulatedTaxableAmount"
        label="累计应纳税所得额"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="accumulatedPrepaidTax"
        label="累计已预缴税额"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="currentTaxAmount"
        label="本期应预扣预缴税额"
        width="180"
      ></el-table-column>
      <!-- <el-table-column
        prop="currentWithholdingTax"
        label="本月已预扣预缴税额"
        width="180"
      ></el-table-column> -->
      <el-table-column
        prop="currentWithholdingTax"
        label="本次应预扣预缴税额"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="vatAmount"
        label="增值税额"
        width="140"
      ></el-table-column>
      <el-table-column
        prop="urbanConstructionTax"
        label="城市维护建设税"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="educationSurcharge"
        label="教育费附加"
        width="140"
      ></el-table-column>
      <el-table-column
        prop="localEducationSurcharge"
        label="地方教育附加"
        width="140"
      ></el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="conditions.total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>

    <el-dialog
      :title="`人员详情（共${overStaffList.length}人）`"
      :visible.sync="showOverStaffDialog"
      width="650px"
      :close-on-click-modal="false"
    >
      <div style="max-height: 400px; overflow-y: auto;">
        <el-table :data="overStaffList"  style="width: 100%" stripe>
          <el-table-column prop="name" label="姓名" width="120" align="center">
            <template slot-scope="scope">
              <span style="font-weight: 500;">{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="idCard" label="身份证号" show-overflow-tooltip>
            <template slot-scope="scope">
              <span style="font-family: 'Courier New', monospace; letter-spacing: 1px;">
                {{ scope.row.idCard }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showOverStaffDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

export default {
  name: 'PayrollDetail',
  data() {
    return {
      loading: true,
      payrollId: null,
      summaryData: {},
      tableData: [],
      conditions: {
        limit: 10,
        offset: 0,
        total: 0
      },
      overStaffList: [],
      showOverStaffDialog: false 
    }
  },
  created() {
    this.payrollId = this.$route.params.id
    this.summaryData = this.$route.query
    this.loadDetails()
    this.loadOverStaffCount()
  },
  methods: {
    async loadDetails() {
      this.loading = true
      try {
        const params = {
          id: this.payrollId,
          limit: this.conditions.limit,
          offset: this.conditions.offset
        }
        const [err, r] = await client.supplierSalaryListPayrollDetail({
          body: params
        })
        if (err) {
          handleError(err)
          return
        }
        this.tableData = r.data.list || []
        this.conditions.total = r.data.total || 0
      } finally {
        this.loading = false
      }
    },
    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.loadDetails()
    },
    async loadOverStaffCount() {
      try {
        const [err, r] = await client.supplierSalarySalaryOverStaffCount({
          body: {
            id: parseInt(this.payrollId)
          }
        })
        if (err) {
          // 静默处理错误
          console.error('获取人员信息失败:', err)
          return
        }
        if (r && r.success && r.data && Array.isArray(r.data)) {
          // 解析返回的数据格式：["姓名-身份证号"]
          this.overStaffList = r.data.map(item => {
            const dashIndex = item.lastIndexOf('-')
            if (dashIndex > 0 && dashIndex < item.length - 1) {
              return {
                name: item.substring(0, dashIndex).trim(),
                idCard: item.substring(dashIndex + 1).trim()
              }
            }
          })
        }
      } catch (error) {
        console.error('调用人员接口异常:', error)
      }
    },
    goBack() {
      this.$router.back()
    }
  }
}
</script>

<style scoped></style>
