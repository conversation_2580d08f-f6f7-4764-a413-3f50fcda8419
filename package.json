{"name": "olading-kit", "version": "0.0.1", "description": "", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "test": "jest", "checkNoUsedComponents": "node scripts/checkNoUsedComponents.js", "checkNoUsedFiles": "node scripts/checkNoUsedFiles.js", "fmt": "node -r esm scripts/fmt.js"}, "repository": {"type": "git", "url": "https://bitbucket.lanmaoly.com/scm/front/kit.git"}, "author": "<EMAIL>", "license": "", "dependencies": {"js-cookie": "3.0.1"}, "devDependencies": {"@babel/preset-env": "7.22.2", "@olading/olading-business-ui": "0.6.9-beat.6.14", "element-ui": "2.15.13", "esm": "3.2.25", "jest": "29.5.0", "jest-environment-jsdom": "29.5.0", "vant": "2.12.54", "vite": "4.3.9", "vite-plugin-vue2": "2.0.3", "vue": "2.6.11", "vue-router": "3.0.7", "vue-template-compiler": "2.6.11"}}