<template >
  <div class="phoneChangeStep2" style="">
    <VerifyIdentidy v-if="step === 0" @next="next" />
    <template v-else>
      <Field
        v-model="phone"
        size="large"
        placeholder="请输入新手机号"
        label="新手机号"
      />
      <Captcha style="width: 100%" v-model="captcha" label="图形验证码" />
      <Otp
        style="width: 100%"
        v-model="otp"
        :captcha="captcha"
        :phone="phone"
        label="验证码"
      />
      <div style="margin: 16px">
        <Button @click="submit" round block type="primary">确定</Button>
      </div>
    </template>
  </div>
</template>

<script>
import { Form, Field, Button } from 'vant'
import Otp from './otp.vue'
import Captcha from './captcha.vue'
import VerifyIdentidy from './verifyIdentidy.vue'
import makePlatformClient from '../../services/platform/makeClient'
import handleError from '../../helpers/handleErrorH5'
import handleSuccess from '../../helpers/handleSuccessH5'
const platformClient = makePlatformClient()

export default {
  components: {
    Form,
    Field,
    Otp,
    Captcha,
    Button,
    VerifyIdentidy
  },
  data() {
    return {
      step: 0,
      captcha: {
        answer: '',
        token: ''
      },
      otp: {
        answer: '',
        token: ''
      },
      oldOtp: {
        answer: '',
        token: ''
      },
      phone: ''
    }
  },

  methods: {
    async submit() {
      const [err, r] = await platformClient.merchantPlatformModifyUser({
        body: {
          user: {
            cellPhone: this.phone
          },
          challenge: this.oldOtp.answer,
          otpToken: this.oldOtp.token,
          otherChallenge: this.otp.answer,
          otherOtpToken: this.otp.token
        }
      })

      if (err) {
        handleError(err)
        return
      }

      handleSuccess('更换成功')
      this.$router.go(-1)
    },
    next(oldOtp) {
      this.step = 1
      this.oldOtp = oldOtp
    }
  }
}
</script>

<style>
</style>