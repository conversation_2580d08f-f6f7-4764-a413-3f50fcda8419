class Client {
  constructor(httpClient) {
    if (!httpClient) {
      throw new Error('httpClient is required')
    }

    this.httpClient = httpClient
  }
  async merchantPlatformProfile(options = {}) {
    const resource = '/api/merchant/platform/profile'
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformGetTimeGreet(options = {}) {
    const resource = '/api/merchant/plat/getTimeGreet'
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformListAppMenu(options = {}) {
    const resource = '/api/merchant/plat/listAppMenu'
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformRenewToken(options = {}) {
    const resource = '/api/merchant/platform/renewToken'
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformLogin(options = {}) {
    const resource = '/api/merchant/platform/login'
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformLogout(options = {}) {
    const resource = '/api/merchant/platform/logout'
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformModifyUser(options = {}) {
    const resource = '/api/merchant/platform/modifyUser'
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformModifyPassword(options = {}) {
    const resource = '/api/merchant/platform/modifyPassword'
    return this.httpClient.request(resource, options)
  }

  async merchantDeptTree(options = {}) {
    const resource = '/api/merchant/dept/dept-tree'
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformListLegal(options = {}) {
    const resource = '/api/merchant/platform/listLegal'
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformListMerchantMember(options = {}) {
    const resource = '/api/merchant/platform/listMerchantMember'
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformListEmployee(options = {}) {
    const resource = '/api/merchant/platform/listEmployee'
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformCreateOtp(options = {}) {
    const resource = `/api/merchant/platform/createOtp`
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformCreateCaptcha(
    options = {
      body: {
        captchaType: 'OAUTH2_4_1'
      }
    }
  ) {
    const resource = `/api/merchant/platform/createCaptcha`
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformListMsg(options = {}) {
    const resource = `/api/merchant/platform/listMsg`
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformUploadFile(options = {}) {
    const resource = `/api/merchant/platform/uploadFile`
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformListFile(options = {}) {
    const resource = `/api/merchant/platform/listFile`
    return this.httpClient.request(resource, options)
  }

  async metchantNetIsMananger(options = {}) {
    const resource = `/api/merchantNet/isMananger`
    return this.httpClient.request(resource, options)
  }

  async adviseAdviseQuery(options = {}) {
    const resource = `/api/advise/adviseQuery`
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformGetProjectPath(options = {}) {
    const resource = `/api/merchant/platform/getProjectPath`
    return this.httpClient.request(resource, options)
  }

  // 获取是否设置安全密码等信息
  async merchantAuthenticatedProfile(options = { method: 'GET' }) {
    const resource = `/api/contract/authenticated/profile`
    return this.httpClient.request(resource, options)
  }

  // 更新安全密码
  async merchantAccountSecurityPasswordUpdate(options = {}) {
    const resource = `/api/merchant/account/personal-password/update`
    return this.httpClient.request(resource, options)
  }

  // ocr
  async merchantAuthOcr(options = {}) {
    const resource = `/api/contract/auth/ocr`
    return this.httpClient.request(resource, options)
  }

  // ocr 第二步确认信息 /api/contract/authenticated/profile/personal/identity
  async merchantAuthenticatedProfileIdentity(options = {}) {
    const resource = `/api/contract/authenticated/profile/personal/identity`
    return this.httpClient.request(resource, options)
  }

  // 活体认证 /api/contract/auth/faceAction
  async merchantAuthFaceAction(options = {}) {
    const resource = `/api/contract/auth/faceAction`
    return this.httpClient.request(resource, options)
  }

  //  安全密码验证
  async merchantUserSecurityVerify(options = {}) {
    const resource = `/api/olading-user/user/securityVerify`
    return this.httpClient.request(resource, options)
  }

  // 工资条已读
  async salaryStubsReviewPayStubs(options = {}, payStubId) {
    const resource =
      `/api/hrsaas-salary/salary/stubs/reviewPayStubs/` + payStubId
    return this.httpClient.request(resource, options)
  }

  // 确认工资条
  async salaryStubsConfirmedPayStubs(options = {}, payStubId) {
    const resource =
      '/api/hrsaas-salary/salary/stubs/confirmedPayStubs/' + payStubId
    return this.httpClient.request(resource, options)
  }

  //钉签约的文件列表
  async oladingFileManagerList(options = {}) {
    const resource = `/api/olading/file-manage/list`
    return this.httpClient.request(resource, options)
  }

  //合同协议员工合同的文件列表
  async elContractGetUserAllElContract(options = {}) {
    const resource = `/api/hrsaas-emp/elcontract/v1/getUserAllElContract`
    return this.httpClient.request(resource, options)
  }

  // 待办钉签约列表
  async oladingReport(options = {}) {
    const resource = `/api/olading/report`
    return this.httpClient.request(resource, options)
  }

  // 待办员工合同列表
  async getUserElContract(options = {}) {
    const resource = `/api/hrsaas-emp/elcontract/v1/getUserElContract`
    return this.httpClient.request(resource, options)
  }

  // 员工合同签署 获取h5地址及token
  async elContractSign(options = {}) {
    const resource = `/api/hrsaas-emp/sign/v1/sign`
    return this.httpClient.request(resource, options)
  }

  // 合同协议 员工合同 查看合同
  async elContractGetContractDetail(options = {}) {
    const resource = `/api/hrsaas-emp/elcontract/v1/getContractDetail`
    return this.httpClient.request(resource, options)
  }

  // 合同协议 钉签约查看合同
  async oladingContractQuery(options = {}) {
    const resource = `/api/olading/contract/query`
    return this.httpClient.request(resource, options)
  }

  // 下载文件
  async downloadFile(options = {}, id) {
    const resource = `/api/merchant/platform/downloadFile/${id}`
    return this.httpClient.request(resource, options)
  }

  // 消息列表
  async merchantMessages(options = {}) {
    const resource = `/api/merchant/advise/adviseQuery2`
    return this.httpClient.request(resource, options)
  }

  // 消息详情
  async merchantMessageDetail(options = {}) {
    const resource = `/api/merchant/advise/adviseDetail`
    return this.httpClient.request(resource, options)
  }

  // 清空消息
  async merchantNoticeClear(options = {}) {
    const resource = `/api/merchant/advise/adviseDelete`
    return this.httpClient.request(resource, options)
  }

  // 全部已读
  async merchantNoticeAllRead(options = {}) {
    const resource = `/api/merchant/advise/allRead`
    return this.httpClient.request(resource, options)
  }

  // 发送邮箱验证码
  async merchantEmailSend(options = {}) {
    const resource = `/api/olading-user/email/send`
    return this.httpClient.request(resource, options)
  }

  // 设置邮箱地址
  async merchantEmailUpdate(options = {}) {
    const resource = `/api/merchant/account/personal-email/update`
    return this.httpClient.request(resource, options)
  }

  // 修改邮箱地址 验证
  async merchantEmailUpdateVerify(options = {}) {
    const resource = `/api/merchant/account/auth-code/verify`
    return this.httpClient.request(resource, options)
  }

  // 下载合同 发送至邮箱
  async merchantSendContractMail(options = {}) {
    const resource = `/api/hrsaas-emp/elcontract/v1/sendContractMail`
    return this.httpClient.request(resource, options)
  }

  // 我的签名 列表
  async profileSignatureQuery(signatureType, options = {}) {
    const resource = `/api/contract/authenticated/profile/signature/query?signatureType=${signatureType}`
    return this.httpClient.request(resource, options)
  }

  // 创建手写签名
  async createSignature(options = {}) {
    const resource = `/api/contract/authenticated/profile/signature/create`
    return this.httpClient.request(resource, options)
  }

  // 创建手写签名
  async setDefaultSignature(options = {}) {
    const resource = `/api/contract/authenticated/profile/signature/set-default`
    return this.httpClient.request(resource, options)
  }

  // 创建手写签名
  async deleteSignature(id, options = {}) {
    const resource = `/api/contract/authenticated/profile/signature/${id}`
    return this.httpClient.request(resource, options)
  }

  // 历史电子档案
  async getUserAllLeaveCompElContract(options = {}) {
    const resource = `/api/hrsaas-emp/elcontract/v1/getUserAllLeaveCompElContract`
    return this.httpClient.request(resource, options)
  }

  // 专家咨询列表
  async queryExpertConsultQuestions(options = {}) {
    const resource = `/api/merchant/consult/queryBelongExpertQuestion`
    return this.httpClient.request(resource, options)
  }

  // 专家咨询详情
  async queryExpertConsultQuestion(options = {}) {
    const resource = `/api/merchant/consult/queryConsultQuestionReply`
    return this.httpClient.request(resource, options)
  }
  // 新增回复
  async addConsultExpertReply(options = {}) {
    const resource = `/api/merchant/consult/expertReply`
    return this.httpClient.request(resource, options)
  }

  async oladingUserUserProfile(options = {}) {
    const resource = `/api/olading-user/user/profile`
    return this.httpClient.request(resource, options)
  }

  async oladingUserMerchantProfile(options = {}) {
    const resource = `/api/olading-user/merchant/profile`
    return this.httpClient.request(resource, options)
  }

  async merchantNetGetMerchantInit(options = {}) {
    const resource = `/api/merchantNet/getMerchantInit`
    return this.httpClient.request(resource, options)
  }

  async contractOpenArchiveUpload(options = {}) {
    const resource = `/api/contract/open/archive/upload`
    return this.httpClient.request(resource, options)
  }

  async contractAuthenticatedProfileSignatureCreate(options = {}) {
    const resource = `/api/contract/authenticated/profile/signature/create`
    return this.httpClient.request(resource, options)
  }

  async hrsaasEmpElcontractV1GetUserAllElContract(options = {}) {
    const resource = `/api/hrsaas-emp/elcontract/v1/getUserAllElContract`
    return this.httpClient.request(resource, options)
  }

  async merchantPlatformDownloadFile(options = {}, { id }) {
    const resource = `/api/merchant/platform/downloadFile/${id}`
    return this.httpClient.request(resource, options)
  }

  async hrSaasSalarySalaryStubsGetWechatYearStubs(options = {}) {
    const resource = '/api/hrsaas-salary/salary/stubs/getWechatYearStubs'
    return this.httpClient.request(resource, options)
  }

  async hrSaasSalarySalaryStubsGetYearStubs(options = {}, year, stubs) {
    const resource = `/api/hrsaas-salary/salary/stubs/getYearStubs/${year}/${stubs}`
    return this.httpClient.request(resource, options)
  }

  async calendarManagementAdd(options = {}) {
    const resource = '/api/calendarManagement/add'
    return this.httpClient.request(resource, options)
  }

  async calendarManagementGetOneDayList(options = {}) {
    const resource = '/api/calendarManagement/getOneDayList'
    return this.httpClient.request(resource, options)
  }

  async calendarManagementGetList(options = {}) {
    const resource = '/api/calendarManagement/getList'
    return this.httpClient.request(resource, options)
  }

  async calendarManagementUpdateStatus(options = {}) {
    const resource = '/api/calendarManagement/updateStatus'
    return this.httpClient.request(resource, options)
  }

  async calendarManagementUpdate(options = {}) {
    const resource = '/api/calendarManagement/update'
    return this.httpClient.request(resource, options)
  }

  async merchantAppletsConfigGetConfigInfo(options = {}) {
    const resource = `/api/merchant/appletsConfig/getConfigInfo`
    return this.httpClient.request(resource, options)
  }

  async attendMobileSignQueryEmployee(options = {}) {
    const resource = `/api/attend/mobile/sign/queryEmployee`
    return this.httpClient.request(resource, options)
  }
  async attendMobileSign(options = {}) {
    const resource = `/api/attend/mobile/sign/doSign`
    return this.httpClient.request(resource, options)
  }

  async attendMobileSignQueryAttendanceGroup(options = {}) {
    const resource = `/api/attend/mobile/sign/queryAttendanceGroup`
    return this.httpClient.request(resource, options)
  }

  async attendMobileSignStatisticAdminDailyData(options = {}) {
    const resource = `/api/attend/mobile/sign/statistic/adminDailyData`
    return this.httpClient.request(resource, options)
  }
  async attendMobileSignStatisticEmployeeMonthlyData(options = {}) {
    const resource = `/api/attend/mobile/sign/statistic/employeeMonthlyData`
    return this.httpClient.request(resource, options)
  }
  async attendMobileSignStatisticEmployeeDailyData(options = {}) {
    const resource = `/api/attend/mobile/sign/statistic/employeeDailyData`
    return this.httpClient.request(resource, options)
  }
  async attendMobileSignStatisticApproveDailyData(options = {}) {
    const resource = `/api/attend/mobile/sign/statistic/approveDailyData`
    return this.httpClient.request(resource, options)
  }
  async sheetProcessDesignInstanceList(options = {}) {
    const resource = `/api/sheet/process-design-instance/list`
    return this.httpClient.request(resource, options)
  }
  async apiPlatMsgGroup(options = {}) {
    const resource = `/api/merchant/plat/msgGroup`
    return this.httpClient.request(resource, options)
  }
  async merchantCheckSafePassword(options = {}) {
    const resource = `/api/merchant/checkSafePassword`
    return this.httpClient.request(resource, options)
  }
  async attendMobileSignFilsUpload(options = {}) {
    options.headers = options.headers || {}
    options.headers['Content-Type'] = 'multipart/form-data'

    const resource = `/api/attend/mobile/sign/files/upload`
    return this.httpClient.request(resource, options)
  }
}

export default Client
