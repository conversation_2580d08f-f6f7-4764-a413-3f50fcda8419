import {
  ContractApprovalStatusWaitingSend,
  ContractApprovalStatusReviewing,
  ContractApprovalStatusPassed,
  ContractApprovalStatusRejected
} from './constants'
export default type => {
  switch (type) {
    case ContractApprovalStatusWaitingSend:
      return '待发送'
    case ContractApprovalStatusReviewing:
      return '审核中'
    case ContractApprovalStatusPassed:
      return '审核通过'
    case ContractApprovalStatusRejected:
      return '审核驳回'
    default:
      throw new Error('not supported yet')
  }
}
