<template>
  <div v-if="isExpert" @click="goExpertConsult">
    <div
      style="
        margin: 0 auto;
        width: 35px;
        height: 35px;
        background: #fdb54f;
        color: #fff;
        text-align: center;
        line-height: 35px;
        border-radius: 50%;
      "
    >
      <span class="icon iconfont">&#xe7ac;</span>
    </div>
    <span style="font-size: 12px"> 专家咨询 </span>
  </div>
</template>

<script>
import icon from '../../../assets/images/icon/Icon-xiuxi.png'

import handleErrorH5 from '../../../helpers/handleErrorH5'
import makePlatformClient from '../../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  name: 'ExpertConsult',

  data() {
    return { isExpert: false, icon }
  },
  async mounted() {
    const [err, r] = await platformClient.queryIsExpert()

    if (err) {
      handleErrorH5(err)
      return
    }

    this.isExpert = r.data.isExpert
  },
  methods: {
    goExpertConsult() {
      this.$router.push('/expertConsultQuestions')
    }
  }
}
</script>

<style>
</style>