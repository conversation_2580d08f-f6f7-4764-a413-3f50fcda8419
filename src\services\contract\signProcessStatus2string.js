import {
  ContractWriteProcessStatusWaitingSend,
  ContractWriteProcessStatusWaitingWrite,
  ContractWriteProcessStatusWrote,
  ContractWriteProcessStatusEmpty
} from './constants'
export default status => {
  const m = [
    { status: ContractWriteProcessStatusWaitingSend, name: '待发送' },
    { status: ContractWriteProcessStatusWaitingWrite, name: '待签' },
    { status: ContractWriteProcessStatusWrote, name: '已签' },
    { status: ContractWriteProcessStatusEmpty, name: '' }
  ]
  const c = m.find(item => item.status === status)
  if (c) {
    return c.name
  }
  return ''
}
