<template>
  <div class="ocrStep1" style="padding: 0 16px">
    <h2>个人实名认证</h2>
    <p
      style="
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #777c94;
        margin: 0 0 40px 0;
      "
    >
      请验证该身份证是您本人在使用，以确保您的账号安全
    </p>
    <div style="display: flex; flex-direction: column; align-items: center">
      <UploadIdCard
        @uploadSuccess="uploadSuccess"
        direction="front"
        style="width: 280px; height: 154px; margin-bottom: 20px"
      />
      <UploadIdCard
        @uploadSuccess="uploadSuccess"
        direction="back"
        style="width: 280px; height: 154px; margin-bottom: 30px"
      />
      <div
        style="
          font-family: PingFangSC-Medium;
          font-weight: 600;
          font-size: 14px;
          color: #46485a;
          text-align: center;
          line-height: 14px;
          display: flex;
          align-items: center;
          margin-bottom: 16px;
        "
      >
        <i
          style="
            width: 60px;
            height: 1px;
            background: #ccc;
            display: inline-block;
          "
        ></i>
        <span style="margin: 0 12px">上传须知</span>
        <i
          style="
            width: 60px;
            height: 1px;
            background: #ccc;
            display: inline-block;
          "
        ></i>
      </div>
      <div
        style="
          font-weight: 400;
          font-size: 12px;
          color: #777c94;
          text-align: center;
          line-height: 12px;
          margin-bottom: 45px;
        "
      >
        <span>请勿边框缺失</span>
        <i style="color: #cbced8; margin: 0 12px">|</i>
        <span>请勿照片模糊</span>
        <i style="color: #cbced8; margin: 0 12px">|</i>
        <span>请勿光斑遮挡</span>
      </div>
    </div>

    <a
      @click="next"
      style="
        color: #fff;
        background: #4f71ff;
        width: 100%;
        margin-right: 10px;
        height: 44px;
        border-radius: 15px;
        line-height: 44px;
        text-align: center;
        display: block;
      "
      >下一步</a
    >
  </div>
</template>

<script>
import { Toast } from 'vant'
import UploadIdCard from './uploadIdCard.vue'
import handleError from '../../../../helpers/handleErrorH5'
import makePlatformClient from '../../../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  components: {
    UploadIdCard
  },
  data() {
    return {
      behindBase64Url: '',
      frontBase64Url: ''
    }
  },
  methods: {
    uploadSuccess({ direction, base64Url }) {
      direction === 'front'
        ? (this.frontBase64Url = base64Url)
        : (this.behindBase64Url = base64Url)
    },
    async next() {
      if (!this.frontBase64Url) {
        handleError({ message: '请上传人像面照片' })
        return
      }

      if (!this.behindBase64Url) {
        handleError({ message: '请上传国徽面照片' })
        return
      }

      const body = {
        behind: {
          idcard: this.behindBase64Url.replace('data:image/jpeg;base64,', '')
        },
        front: {
          idcard: this.frontBase64Url.replace('data:image/jpeg;base64,', '')
        }
      }

      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        duration: 0
      })

      const [err, r] = await platformClient.merchantAuthOcr({ body })

      if (err) {
        handleError({ message: '请上传正确的身份证正反面' })
        return
      }
      Toast.clear()

      this.$emit('ocr', r.data)
    }
  }
}
</script>

<style>
</style>