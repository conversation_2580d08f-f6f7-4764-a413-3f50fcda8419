<template>
  <Popup v-model="showPopup" round style="width: 300px; padding: 30px 0">
    <h3 style="text-align: center; margin-bottom: 30px">认证失败</h3>
    <div style="padding: 0 10px 0 40px; margin-bottom: 40px">
      <p>{{ errorMessage }}</p>
    </div>
    <div
      style="
        padding: 0 30px;
        display: flex;
        align-items: center;
        justify-content: center;
      "
    >
      <a
        @click="retry"
        style="
          color: #fff;
          background: #4f71ff;
          width: 130px;
          margin-right: 10px;
          height: 44px;
          border-radius: 30px;
          line-height: 44px;
          text-align: center;
        "
        >重试</a
      >
      <a
        @click="back"
        style="
          width: 130px;
          margin-right: 10px;
          height: 44px;
          border: 1px solid #4f71ff;
          border-radius: 30px;
          line-height: 44px;
          text-align: center;
          color: #4f71ff;
        "
        >返回</a
      >
    </div>
  </Popup>
</template>

<script>
import { Popup, Button } from 'vant'
export default {
  components: {
    <PERSON><PERSON>,
    But<PERSON>
  },
  data() {
    return {
      showPopup: false,
      errorMessage: '人脸认证未通过'
    }
  },
  methods: {
    open(message) {
      this.errorMessage = message || '人脸认证未通过'
      this.showPopup = true
    },
    close() {
      this.showPopup = false
    },
    retry() {
      this.close()
      this.$emit('retry')
    },
    back() {
      this.close()
      this.$emit('back')
    }
  }
}
</script>

<style scoped>
h3,
p {
  margin: 0;
}
p {
  margin-bottom: 10px;
}
</style>
