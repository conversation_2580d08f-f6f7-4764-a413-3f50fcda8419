<template>
  <div
    class="phoneChange"
    style="min-height: 100vh; background: #f2f3f7; padding: 13px 12px 0"
  >
    <VerifyIdentidyH5 v-show="step === 0" @next="next" />
    <div v-show="step === 1">
      <Form ref="form" :model="form">
        <Field
          v-model="form.phone"
          placeholder="请输入新手机号"
          label="新手机号"
          :rules="rules.phone"
          type="tel"
          clearable
          maxlength="11"
        />
        <Captcha
          style="width: 100%"
          v-model="captcha"
          label="图形验证码"
          :rules="rules.captcha"
        />
        <Otp
          class="otp"
          style="width: 100%"
          v-model="form.code"
          :captcha="captcha"
          :phone="form.phone"
          label="验证码"
          :rules="rules.code"
          @otp="onOtp"
        />
      </Form>
      <div style="margin: 16px 0; display: flex; gap: 16px">
        <Button style="border-radius: 6px" @click="goBack" round block
          >返回</Button
        >
        <Button
          style="border-radius: 6px"
          @click="confirmEdit"
          round
          block
          type="primary"
          >下一步</Button
        >
      </div>
    </div>
  </div>
</template>

<script>
import { Form, Field, Button, Toast } from 'vant'
import Otp from 'kit/pages/marketing/promoter/loginOtp.vue'
import Captcha from 'kit/pages/marketing/promoter/loginCaptcha.vue'
import VerifyIdentidyH5 from './verifyIdentidyH5.vue'
import handleError from 'kit/helpers/handleErrorH5'
import handleSuccess from 'kit/helpers/handleSuccessH5'
import * as reg from 'kit/helpers/regexp'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

export default {
  components: {
    Form,
    Field,
    Otp,
    Captcha,
    Button,
    VerifyIdentidyH5
  },
  data() {
    return {
      step: 0,
      form: {
        phone: '',
        code: ''
      },
      captcha: {
        answer: '',
        token: ''
      },
      otp: {
        answer: '',
        token: ''
      },
      oldOtp: {
        answer: '',
        token: ''
      },
      rules: {
        phone: [
          { required: true },
          {
            pattern: reg.PHONE_NUMBER_REGEX,
            message: '请输入有效的手机号'
          }
        ],
        captcha: [
          { required: true },
          {
            pattern: reg.VERIFICATION_IMG_CODE_REGEX,
            message: '验证码必须为4位数字'
          }
        ],
        code: [
          { required: true },
          {
            pattern: reg.VERIFICATION_SMS_CODE_REGEX,
            message: '验证码必须为6位数字'
          }
        ]
      }
    }
  },

  methods: {
    onOtp(otp) {
      this.otp = {
        ...otp
      }
    },
    async confirmEdit() {
      if (this.form.phone === this.$route.query.mobile) {
        Toast.fail('请输入新手机号，不能输入原有手机号')
        return
      }
      if (!this.otp.token) {
        Toast.fail('请输入短信验证码')
        return
      }
      const [err, r] = await marketingClient.mobilePromoterChangeMobile({
        body: {
          newMobile: this.form.phone,
          newSmsCode: this.otp.answer,
          newSmsToken: this.otp.token
        }
      })

      if (err) {
        handleError(err)
        return
      }

      handleSuccess('更换成功')
      this.$router.go(-1)
    },
    next() {
      this.step = 1
    },
    goBack() {
      this.step = 0
    }
  }
}
</script>

<style scoped>
::v-deep .van-cell {
  padding: 14px 16px 15px 17px;
}
::v-deep .otp .van-cell {
  border-radius: 0 0 8px 8px;
}
::v-deep .van-field__body {
  height: 22px;
  line-height: 22px;
}
::v-deep .van-field__label {
  font-size: 14px;
  color: #4e5769;
  line-height: 22px;
}
</style>
