<template>
  <Detail :payroll="payroll" :currentItemObj="currentItemObj" />
</template>

<script>
import handleError from 'kit/helpers/handleErrorH5'
import Detail from '../../components/oladingH5/detailH5.vue'

import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  components: {
    Detail
  },
  data() {
    return {
      payroll: [],
      payStubsId: this.$route.query.payStubsId,
      year: this.$route.query.year,
      currentItemObj: {
        name: '',
        mobile: ''
      }
    }
  },
  async created() {
    const [err, r] =
      await platformClient.hrSaasSalarySalaryStubsGetWechatYearStubs({
        body: {
          year: this.year || '',
          stubs: this.$route.query.stubs,
          source: 'WECHAT'
        }
      })
    if (err) {
      handleError(err)
      return
    }

    if (r.data && r.data.stubsList && r.data.stubsList.length) {
      for (var c of r.data.stubsList) {
        if (c.salaryMonthStubs && c.salaryMonthStubs.length) {
          const arr = c.salaryMonthStubs[0].salaryStubsInfos.find(
            item => item.payStubsId === this.payStubsId
          )
          if (arr && arr.salaryStubsGroups.length) {
            this.payroll = arr.salaryStubsGroups
          }
        }
      }
    }
    this.currentItemObj.name = r.data.name
    if (r.data.mobile) {
      this.currentItemObj.mobile =
        r.data.mobile.toString().slice(0, 3) +
        '****' +
        r.data.mobile.toString().slice(-4)
    }
  }
}
</script>

<style scoped>
</style>