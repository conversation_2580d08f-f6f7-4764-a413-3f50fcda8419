<template>
  <div class="mine" style="background: #f5f5f5" v-if="profile.userId">
    <div class="baseInfo">
      <div
        style="display: flex; align-items: center; padding: 0.24rem 0 0 0.24rem"
      >
        <div
          style="
            width: 52px;
            height: 52px;
            border-radius: 999px;
            background: #ffffffff;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
          "
        >
          <i
            class="icon iconfont icon-base-user-icon"
            style="color: #f77234; font-size: 24px"
          ></i>
        </div>
        <div>
          <div style="color: #ffffff; font-size: 20px">
            {{ profile.userName }}
          </div>
          <div style="display: flex; align-items: center">
            <span
              style="
                max-width: 192px;
                color: #ffffff;
                font-size: 12px;
                margin-right: 9px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              "
            >
              {{ this.currentMerchant }}
            </span>
            <i
              v-if="profile.merchantList && profile.merchantList.length > 1"
              style="color: #ffffff"
              class="icon iconfont icon-direction-interaction-swap"
              @click="switchEnterprise"
            ></i>
          </div>
        </div>
      </div>
    </div>
    <div
      style="
        display: flex;
        align-items: center;
        justify-content: space-around;
        width: 7.02rem;
        height: 1.72rem;
        margin: 0 0.24rem;
        background: linear-gradient(180.2deg, #fff0e6 0%, #ffffff 100%);
        position: absolute;
        top: 1.54rem;
        border-radius: 8px;
        font-size: 13px;
        font-weight: 400;
      "
    >
      <div style="text-align: center">
        <div style="color: #20202; font-size: 24px; font-weight: 700">
          {{ formatNumber(profile.totalActivityCount) }}
        </div>
        <span style="color: #bbbcc4">累计活动场次</span>
      </div>
      <span
        style="width: 1px; height: 43px; opacity: 1; background: #e4e7ed"
      ></span>
      <div style="text-align: center">
        <div style="color: #20202; font-size: 24px; font-weight: 700">
          {{ formatNumber(profile.totalCouponCount) }}
        </div>
        <span style="color: #bbbcc4">累计发放优惠券</span>
      </div>
    </div>
    <div
      style="
        height: calc(100vh - 218px);
        background: #f5f5f5;
        margin: 100px 12px 0;
        box-sizing: border-box;
        position: relative;
        top: -30px;
      "
    >
      <Card>
        <CellGroup inset>
          <Cell
            @click="goPhoneChange"
            size="large"
            title="手机号"
            :value="hiddenPhone(profile.mobile)"
            is-link
          />
        </CellGroup>
      </Card>
      <div
        style="
          width: 100vw;
          left: 0;
          position: fixed;
          bottom: 0;
          background: #ffffff;
        "
      >
        <Actions />
      </div>
    </div>
    <Popup
      style="border-radius: 8px 8px 0 0"
      v-model="showPicker"
      position="bottom"
    >
      <Picker
        class="picker"
        title="切换企业"
        show-toolbar
        :columns="columns"
        :default-index="defaultIndex"
        @confirm="onConfirm"
        @cancel="cancel"
      >
        <template #option="option">
          <div style="width: 100%; display: flex; align-items: center">
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                border-radius: 8px;
                opacity: 1;
                background: #f0f5ff;
                margin-right: 8px;
              "
            >
              <i
                style="color: #2f54eb"
                class="icon iconfont icon-application-hierarchy"
              ></i>
            </div>
            <div
              style="
                font-size: 14px;
                line-height: 24px;
                margin-right: 10px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                flex: 1;
              "
            >
              {{ option.name }}
            </div>
            <i
              v-if="option.id === profile.currentMerchantId"
              style="color: #f77234; font-size: 18px"
              class="icon iconfont icon-remind-complete"
            ></i>
          </div>
        </template>
      </Picker>
    </Popup>
  </div>
</template>

<script>
import { Popup, Picker, Cell, CellGroup, Toast } from 'vant'
import Actions from 'kit/components/marketing/promoter/menus.vue'
import Card from 'kit/components/mpH5/workbench/cardH5.vue'
import formatNumber from 'kit/formatters/number'
import { setToken } from 'kit/helpers/token'
import handleError from 'kit/helpers/handleErrorH5'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

export default {
  components: {
    Actions,
    Card,
    Popup,
    Picker,
    Cell,
    CellGroup
  },
  data() {
    return {
      showPicker: false,
      currentMerchant: '',
      columns: [],
      profile: {
        merchantList: []
      },
      setSecurityPassword: false
    }
  },
  computed: {
    defaultIndex() {
      return this.profile.merchantList.findIndex(
        item => item.id === this.profile.currentMerchantId
      )
    }
  },
  async created() {
    this.loadProfile()
  },
  methods: {
    formatNumber,
    async loadProfile() {
      const [err, r] = await marketingClient.mobilePromoterProfile({
        method: 'GET'
      })
      if (err) {
        handleError(err)
        return
      }
      this.profile = r.data
      this.columns = r.data.merchantList

      for (var c of r.data.merchantList) {
        if (c.id == r.data.currentMerchantId) {
          this.currentMerchant = c.name
        }
      }
    },
    switchEnterprise() {
      this.showPicker = true
      this.loadProfile()
    },
    cancel() {
      this.showPicker = false
    },
    async onConfirm(value) {
      const [err, r] = await marketingClient.mobilePromoterChangeMerchant({
        body: {
          id: value.id
        }
      })
      if (err) {
        handleError(err)
        return
      }
      setToken(r.data.token)
      Toast.success('切换成功')
      this.showPicker = false
      this.loadProfile()
    },
    goPhoneChange() {
      this.$router.push({
        path: '/phoneChange',
        query: { mobile: this.profile.mobile }
      })
    },
    goSecurityPassword() {
      if (this.setSecurityPassword) {
        this.$router.push('/securityPasswordRemember')
        return
      }
      this.$router.push('/securityPasswordForget')
    },
    hiddenPhone(phone) {
      if (phone) {
        let newPhone = phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
        return newPhone
      }
    }
  }
}
</script>

<style scoped>
.baseInfo {
  width: 7.5rem;
  height: 2.24rem;
  background-image: url('kit/assets/images/<EMAIL>');
  background-size: cover;
}
::v-deep .van-cell-group--inset {
  margin: 0;
}
::v-deep .van-cell__title {
  font-size: 14px;
  color: #4e5769;
}
::v-deep .van-cell__value {
  color: #1e2228;
}
::v-deep .van-picker-column__item {
  border-radius: 8px;
  margin: 0 16px;
  padding: 0 12px;
}
::v-deep .van-picker-column__item--selected {
  background: #f2f4f7;
}
::v-deep .van-hairline-unset--top-bottom.van-picker__frame::after {
  content: none !important;
}
</style>
