<template>
  <div>
    <List
      :employeePayrolls="employeePayrolls"
      :currentYear="currentYear"
      @goDetail="payrollDetail"
      @refresh="getPayrolls"
    />
  </div>
</template>

<script>
import handleError from 'kit/helpers/handleErrorH5'
import List from '../../components/oladingH5/listH5.vue'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()
export default {
  components: {
    List
  },
  data() {
    return {
      employeePayrolls: [],
      stubs: this.$route.query.stubs,
      currentYear: this.$route.query.year,
      selectedYear: ''
    }
  },
  created() {
    var value = this.$route.query.year
    this.getPayrolls(value)
  },
  methods: {
    async getPayrolls(value) {
      this.selectedYear = value
      const [err, r] =
        await platformClient.hrSaasSalarySalaryStubsGetWechatYearStubs({
          body: {
            year: value,
            stubs: this.stubs,
            source: 'WECHAT'
          }
        })
      if (err) {
        handleError(err)
        return
      }
      // this.year = r.data.stubsList[0].salaryYearStubsName
      this.employeePayrolls = []
      if (r.data && r.data.stubsList && r.data.stubsList.length) {
        for (var c of r.data.stubsList) {
          if (c.salaryMonthStubs && c.salaryMonthStubs.length) {
            this.employeePayrolls.push(c.salaryMonthStubs[0])
          }
        }
      }
    },
    payrollDetail(value) {
      this.$router.push({
        path: './payroll',
        query: {
          payStubsId: value.payStubsId,
          year: this.selectedYear,
          stubs: this.stubs
        }
      })
    }
  }
}
</script>

<style scoped>
</style>