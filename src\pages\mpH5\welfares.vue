<template>
  <div
    class="welfare"
    v-if="welfares"
    style="color: #fff; padding: 20px 20px 0px 20px"
  >
    <div v-if="welfares && !welfares.length">
      暂未为您开通福利上传，请联系管理员
    </div>
    <div
      class="welfare"
      style="display: flex; height: 66px; padding: 17px; gap: 10px"
      :style="{ background: `url(${bg})`, backgroundSize: '100% 100%' }"
      :key="index"
      v-for="(welfare, index) in welfares"
      @click="handleReserve(welfare)"
    >
      <img :src="logo" style="width: 38px; height: 38px" />
      <div>
        <h3 style="margin: 0">{{ welfare.name }}</h3>
        {{ welfare.description }}
        <br />
        <a
          plain
          hairline
          style="
            cursor: pointer;
            padding: 4px 6px;
            border-radius: 8px;
            border: 1px solid #fff;
            position: relative;
            top: 10px;
          "
          >立即换购</a
        >
      </div>
    </div>
  </div>
</template>

<script>
import { Button, Dialog, Loading } from 'vant'
import logo from 'kit/assets/images/hfd_mall_logo.png'
import bg from 'kit/assets/images/hfd_mall_bg.png'
import makeClient from 'kit/services/platform/makeClient'
import handleError from 'kit/helpers/handleErrorH5'
const platformClient = makeClient()
export default {
  components: {
    Button,
    Loading,
    Dialog
  },
  data() {
    return {
      logo,
      bg,
      welfares: null
    }
  },
  created() {
    this.loadWelfares()
  },
  methods: {
    async loadWelfares() {
      const [err, r] = await platformClient.welfareMallList()
      if (err) {
        handleError(err)
        return
      }
      this.welfares = r.data || []
    },
    async handleReserve(welfare) {
      const [err, r] = await platformClient.welfareMallUrl({
        body: {
          id: welfare.id,
          appType: '2'
        }
      })
      if (err) {
        handleError(err)
        return
      }

      if (r && r.data && r.data.url) {
        window.location.href = r.data.url
      } else {
        Dialog({
          title: r.data.title,
          message: r.data.message
        })
      }
    }
  }
}
</script>

<style></style>
