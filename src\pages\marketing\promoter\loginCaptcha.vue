<template>
  <div
    style="width: 100%; position: relative; display: flex; align-items: center"
  >
    <Field
      placeholder="请输入图形验证码"
      @input="changeAnswer"
      maxlength="4"
      :label="label"
      size="large"
      type="tel"
      @clear="clearAnswer"
      v-model="answer"
      :rules="rules"
    >
    </Field>
    <img
      style="position: absolute; right: 0.16rem; height: 30px"
      :src="src"
      @click="load()"
      v-if="token"
    />
  </div>
</template>

<script>
import { Field } from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
import makeMarketingClient from 'kit/services/marketing/makeClient'
const marketingClient = makeMarketingClient()

export default {
  components: {
    Field
  },
  props: {
    label: {
      type: String,
      default: ''
    },
    rules: Array
  },
  created() {
    this.load()
  },
  computed: {
    src() {
      const baseUrl = window.env.api
      if (!baseUrl) {
        handleError('无法获取平台的API地址')
        return
      }
      return `${baseUrl}/marketing/sms/imageCaptcha?token=${encodeURIComponent(
        this.token
      )}`
    }
  },
  data() {
    return {
      token: '',
      answer: ''
    }
  },
  methods: {
    clearAnswer() {
      this.answer = ''
      this.$emit('input', {})
      this.load()
    },
    changeAnswer(v) {
      if (v.length === 4) {
        this.$emit('input', {
          token: this.token,
          answer: this.answer
        })
      }
    },
    async load() {
      const [err, r] = await marketingClient.smsCreateImageCaptcha()
      if (err) {
        handleError(err)
        return
      }

      if (!r.data) {
        handleError('验证码获取失败')
        return
      }

      this.token = r.data
    }
  }
}
</script>

<style></style>
