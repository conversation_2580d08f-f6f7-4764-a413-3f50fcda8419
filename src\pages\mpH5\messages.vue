<template>
  <div style="background: #f6f6f6; min-height: 100vh">
    <div
      style="
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        background: #fff;
      "
    >
      <TypePicker @input="selectType" style="flex: 1" />
      <div style="font-size: 14px; line-height: 44px">
        <span @click="markRead"
          ><i
            class="iconfont icon-base-flag"
            style="vertical-align: middle; margin-right: 5px"
          ></i
          >标记已读</span
        >
        <span @click="clearRead" style="margin: 0 20px"
          ><i
            class="iconfont icon-media-notification-off"
            style="vertical-align: middle; margin-right: 5px"
          ></i
          >清空已读</span
        >
      </div>
    </div>
    <div>
      <Item
        style="margin-bottom: 10px"
        v-for="message in messages"
        :key="message.id"
        :message="message"
        @detail="detail"
      />
    </div>
  </div>
</template>

<script>
import TypePicker from '../../components/mpH5/messages/typePicker.vue'
import Item from '../../components/mpH5/messages/item.vue'
import handleError from '../../helpers/handleErrorH5'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  components: {
    Item,
    TypePicker
  },
  data() {
    return {
      applicationSource: '',
      noticeType: '',
      terminal: 'MOBILE',
      messages: []
    }
  },
  async created() {
    this.loadMessages()
  },
  methods: {
    async detail(message) {
      if (message.link) {
        await platformClient.merchantMessageDetail({
          body: {
            adviseId: message.id,
            noticeType: message.noticeType
          }
        })

        if (message.link.indexOf('http') == -1) {
          this.$router.push(message.link)
        } else {
          // 标记已读
          window.location.href = message.link
        }
        return
      }

      this.$router.push(`message/${message.id}/${message.noticeType}`)
    },
    async loadMessages() {
      const [err, r] = await platformClient.merchantMessages({
        body: {
          adivseFilters: {
            applicationSource: this.applicationSource,
            noticeType: this.noticeType,
            terminal: this.terminal
          },
          limit: 9999,
          start: 0
        }
      })

      if (err) {
        return handleError(err)
      }

      this.messages = r.data.list
    },
    selectType(type) {
      this.noticeType = type.value
      this.loadMessages()
    },
    async markRead() {
      const [err, r] = await platformClient.merchantNoticeAllRead({
        body: { noticeType: this.noticeType || 'ALL', read: true }
      })

      if (err) {
        handleError(err)
        return
      }

      this.loadMessages()
    },
    async clearRead() {
      const [err, r] = await platformClient.merchantNoticeClear({
        body: { noticeType: this.noticeType || 'ALL' }
      })

      if (err) {
        handleError(err)
        return
      }

      this.loadMessages()
    }
  }
}
</script>

<style>
</style>