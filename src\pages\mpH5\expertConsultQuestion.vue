<template>
  <div style="padding: 0 20px 80px 20px">
    <QuestionInfo
      :title="question.title"
      :consultType="question.questionTypeName"
      :consultUserName="question.consultUserName"
      :consultTime="question.createTime"
    />
    <div style="padding:25px 0">
      {{ question.content }}
    </div>
    <ReplyBox :count="question.replyNumber">
      <ReplyItem
        v-for="(reply, index) in question.list"
        :key="index"
        :replyUserName="reply.replyUserName"
        :replyTime="reply.createTime"
        :content="reply.content"
      />
    </ReplyBox>
    <ReplyInput @confirm="reply" ref="replyInput" />
    <div style="    position: fixed;
    bottom: 0px;
    width: 100vw;
    left: 0;
    height: 80px;
    background: #fff;">
      <Button  @click="$refs.replyInput.open()" type="primary" block round
      >回复</Button
    >
    </div>
  </div>
</template>

<script>
import { Toast, But<PERSON> } from 'vant'
import QuestionInfo from '../../components/mpH5/expertConsult/questionInfo.vue'
import ReplyBox from '../../components/mpH5/expertConsult/replyBox.vue'
import ReplyItem from '../../components/mpH5/expertConsult/replyItem.vue'
import ReplyInput from '../../components/mpH5/expertConsult/replyInput.vue'

import handleError from '../../helpers/handleErrorH5'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  components: {
    QuestionInfo,
    ReplyBox,
    ReplyItem,
    ReplyInput,
    Button
  },
  data() {
    return {
      question: {}
    }
  },
  async created() {
    this.loadConsultQuestion()
  },
  methods: {
    async loadConsultQuestion() {
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        loadingType: 'spinner'
      })

      const consultQuestionId = this.$route.params.id
      const [err, r] = await platformClient.queryExpertConsultQuestion({
        body: { consultQuestionId }
      })

      if (err) {
        handleError(err)
        return
      }

      Toast.clear()

      this.question = r.data
    },
    async reply(content) {
      const [err, r] = await platformClient.addConsultExpertReply({
        body: { consultQuestionId: this.question.consultQuestionId, content }
      })

      if (err) {
        handleError(err)
        return
      }

      this.loadConsultQuestion()
    },
    detail(id) {
      this.$router.push({ path: `/expertConsultQuestion/${id}` })
    }
  }
}
</script>

<style>
</style>