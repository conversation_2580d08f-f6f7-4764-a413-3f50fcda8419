<template>
  <div class="contractDetail" style="color: #666">
    <div style="padding: 20px">
      <h3 style="margin: 0">{{ contract.contractName }}</h3>
      <span style="color: #666">{{ staffTemplateType[contract.contractFileType] }}</span>
    </div>
    
    <div style="background: #f5f5f5; padding: 10px; text-align: right">
      <span style="color: #4f71ff">{{
        elContractStatus[contract.contractSignStatus]
      }}</span>
    </div>

    <div style="padding: 20px 0">
      <Steps direction="vertical" active-icon="clock" :active="stepActive">
        <Step v-for="(stepLog, index) in contract.steps" :key="index">
          <span
            style="
              display: inline-block;
              background: #d8f0f6;
              color: #4f71ff;
              width: 20px;
              height: 20px;
              text-align: center;
            "
            >{{ nodeType[stepLog.operate] }}</span
          >

          <span style="color: #666; margin: 0 10px">{{
            stepLog.userName
          }}</span>
          
          <span style="color: #666; float: right">{{
            stepLog.signTime
          }}</span>
        </Step>
      </Steps>
    </div>

    <div
      v-if="contract.handleInfo"
      style="padding: 20px; background: #f5f5f5"
      class="file-handleInfo"
    >
      <strong>被退回原因</strong>
      <p>{{ contract.handleInfo }}</p>
    </div>
  </div>
</template>

<script>
import { Step, Steps } from 'vant'
import { elContractStatus } from '../../formatters/mpH5/constants'
import { staffTemplateType, nodeType } from '../../formatters/mpH5/constants'
import dateTime from '../../formatters/dateTime'
import handleErrorH5 from '../../helpers/handleErrorH5'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  name: 'contractDetail',
  components: {
    Step,
    Steps
  },
  computed: {
    stepActive() {
      let active = 0

      this.contract?.steps?.forEach((stepLog, index) => {
        if (stepLog.signTime) {
          active = index
        }
      })

      if (active === this.contract.steps?.length - 1) {
        return active + 1
      }

      return active
    }
  },
  data() {
    return {
      contract: {},
      staffTemplateType,
      nodeType,
      elContractStatus
    }
  },
  async created() {
    const contractId = this.$route.params.id
    const stepId = this.$route.params.stepId
    const [err, r] = await platformClient.elContractGetContractDetail({
      body: {
        contractId,
        stepId
      }
    })

    if (err) {
      handleErrorH5(err)
      return
    }

    this.contract = r.data
  },
  methods: {
   
  }
}
</script>

<style>
</style>