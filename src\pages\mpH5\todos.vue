<template>
  <div style="background: #f5f5f5; min-height: 100vh; padding: 20px">
    <template v-if="todos.length > 0">
      <div
        v-for="(todo, index) in todos"
        :key="index"
        @click="goSign(todo.contractId, todo.silenceSignYn)"
        style="
          display: flex;
          justify-content: space-between;
          background: #fff;
          padding: 20px 0 20px 20px;
          border-radius: 8px;
          margin-bottom: 15px;
        "
      >
        <div>
          <h3 style="margin: 0; margin-bottom: 20px; width: 180px">
            {{ todo.contractName }}
          </h3>
          <div>
            <span
              style="
                background: #ffe8e6;
                color: #f43e3e;
                margin-right: 5px;
                display: inline-block;
                width: 15px;
                height: 15px;
              "
              >发</span
            >
            <span style="color: #666">{{ todo.taxSubName }}</span>
          </div>
        </div>
        <div style="display: flex">
          <div
            style="
              text-align: right;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
            "
          >
            <div style="margin-bottom: 20px; color: #6a6f7f">
              {{ todo.createdTime }}
            </div>
            <div style="color: #feab05">
              {{ elContractStatus[todo.contractSignStatus] }}
            </div>
          </div>
          <div style="display: flex; align-items: center; padding: 0 20px">
            <i
              style="color: #666"
              class="iconfont icon-direction-arrow-border-right"
            >
            </i>
          </div>
        </div>
      </div>
    </template>
    <NoData v-else style="margin: 100px auto; width: 200px" />
    <div
      style="
        width: 100vw;
        left: 0;
        position: fixed;
        bottom: 0;
        background: #ffffff;
      "
    >
      <Actions defaultAction="todos" />
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant'
import handleErrorH5 from '../../helpers/handleErrorH5'
import { elContractStatus } from '../../formatters/mpH5/constants'
import Actions from '../../components/mpH5/workbench/menusH5.vue'
import NoData from '../../components/ui/svgIcon/noData.vue'
import makePlatformClient from '../../services/platform/makeClient'
const platformClient = makePlatformClient()

export default {
  name: 'contracts',
  components: {
    Actions,
    NoData
  },
  data() {
    return {
      todos: [],
      elContractStatus
    }
  },
  methods: {
    async goSign(contractId, silenceSignYn) {
      Toast.loading({
        message: '加载中...',
        forbidClick: true
      })

      const [err, r] = await platformClient.elContractSign({
        body: { contractId, silenceSignYn, clientSource: 'WECHAT_APPLET' }
      })

      if (err) {
        handleErrorH5(err)
        return
      }

      Toast.clear()
      const url = `${r.data.mobileUrl}?token=${r.data.token}&callback=${window.location.href}`
      window.location.href = url
    }
  },
  async created() {
    Toast.loading({
      message: '加载中...',
      forbidClick: true
    })

    const [err, r] = await platformClient.getUserElContract()

    if (err) {
      handleErrorH5(err)
      return
    }

    Toast.clear()
    this.todos = r.data
  }
}
</script>

<style>
</style>