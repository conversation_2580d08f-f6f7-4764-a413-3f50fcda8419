<template>
  <div class="payrolls-new-container">
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      label-width="120px"
      style="width: 600px"
    >
      <el-form-item label="客户" prop="customerId">
        <SupplierCustomersSelector
          :multiple="false"
          v-model="form.customerId"
          placeholder="请选择客户"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="作业主体" prop="supplierCorporationId">
        <el-select
          filterable
          v-model="form.supplierCorporationId"
          placeholder="请选择作业主体"
          style="width: 100%"
        >
          <el-option v-for="supplier in supplierOptions" :key="supplier.id" :label="supplier.name" :value="supplier.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="服务合同" prop="contractId">
        <el-select
          filterable
          v-model="form.contractId"
          placeholder="请选择服务合同"
          style="width: 100%"
          :disabled="!form.customerId || !form.supplierCorporationId"
        >
          <el-option v-for="contract in contractOptions" :key="contract.id" :label="contract.name" :value="contract.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="税款所属期" prop="taxPeriod">
        <el-date-picker
          v-model="form.taxPeriod"
          type="month"
          placeholder="选择月份"
          format="yyyy-MM"
          value-format="yyyy-MM"
          style="width: 100%"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="个税申报月">
        <el-input :value="taxDeclarationMonth" readonly></el-input>
      </el-form-item>
      <el-form-item label="上传应发文件" prop="file">
        <!-- <ImportUpload taxCalculationMethod="PAYROLL_ADD" v-model="fileList" @getFileParams="getFileParams" />
        <p v-if="showErrInfo">
        您有<span style="color: red; margin: 0 5px">{{ failCount }}</span
        >条数据导入失败，<span
          style="color: green; cursor: pointer"
          @click="downloadErrFile"
          >点击下载错误文件</span
        >
      </p> -->
        <input
          ref="fileInput"
          type="file"
          @change="handleFileChange"
          style="display: none"
          accept=".xlsx, .xls"
        />
        <div style="display: flex; align-items: center; width: 100%">
          <el-input
            :value="form.file ? form.file.name : ''"
            placeholder="请选择文件"
            readonly
            style="flex-grow: 1"
          >
            <el-button slot="append" @click="triggerFileInput"
              >选择文件</el-button
            >
          </el-input>
          <el-button
            type="text"
            @click="downloadTemplate"
            style="margin-left: 10px"
            >下载模板</el-button
          >
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit" :loading="loading"
          >创建</el-button
        >
        <el-button @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>

    <!-- 错误数据对话框 -->
    <el-dialog
      title="应发文件导入结果"
      :visible.sync="showErrorDialog"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div style="text-align: center; padding: 20px">
        <div style="margin-bottom: 20px">
          <span style="font-size: 16px; color: #333">
            校验通过数据已处理完成，校验未通过数据请导出错误数据处理。
          </span>
        </div>
        <el-button type="primary" style="margin-top: 20px;" @click="exportErrorData">
          导出错误数据
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { exportExcel } from 'kit/helpers/exportExcel'
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import { getToken } from '../../../helpers/token'
import SupplierCustomersSelector from './selector/supplierCustomers.vue'
import CorporationsSelector from './selector/corporations.vue'
import ServiceContractsSelector from './selector/serviceContracts.vue'
import ImportUpload from './components/importUpload.vue'

const client = makeClient()

// 获取上一个月的 YYYY-MM 格式字符串
function getPreviousMonth() {
  const date = new Date()
  date.setMonth(date.getMonth() - 1)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  return `${year}-${month}`
}

// 获取当前月的 YYYY-MM 格式字符串
function getCurrentMonth() {
  const date = new Date()
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  return `${year}-${month}`
}

  export default {
  name: 'PayrollsNew',
  components: {
    SupplierCustomersSelector,
    CorporationsSelector,
    ServiceContractsSelector,
    ImportUpload
  },
  data() {
    return {
      loading: false,
      form: {
        customerId: null,
        supplierCorporationId: null,
        contractId: null,
        taxPeriod: getPreviousMonth(),
        file: null // This will hold the File object
      },
      supplierOptions: [],
      contractOptions: [],
      headerToken: {
        Authorization: `Bearer ${getToken()}`
      },
      showErrorDialog: false,
      errorUuid: '',
      rules: {
        customerId: [
          { required: true, message: '请选择客户', trigger: 'change' }
        ],
        supplierCorporationId: [
          { required: true, message: '请选择作业主体', trigger: 'change' }
        ],
        contractId: [
          { required: true, message: '请选择服务合同', trigger: 'change' }
        ],
        taxPeriod: [
          { required: true, message: '请选择税款所属期', trigger: 'change' }
        ],
        file: [{ required: true, message: '请上传应发文件', trigger: 'blur' }]
      },
      fileList: [],
      showErrInfo: false,
      failCount: 0,
      uuid: ''
    }
  },
  computed: {
    taxDeclarationMonth() {
          if (!this.form.taxPeriod) return '';
      
          // 解析taxPeriod (格式为 "YYYY-MM")
          const [year, month] = this.form.taxPeriod.split('-').map(Number);
      
          // 计算下一个月
          let nextMonth = month + 1;
          let nextYear = year;
      
          // 如果是12月，则下一个月是下一年的1月
          if (nextMonth > 12) {
            nextMonth = 1;
            nextYear += 1;
          }
      
          // 格式化为 "YYYY-MM"
          return `${nextYear}-${String(nextMonth).padStart(2, '0')}`;
        }
  },
  watch: {
    'form.customerId'(val) {
      this.form.contractId = null
      this.loadContractOptions()
    },
    'form.supplierCorporationId'(val) {
      this.form.contractId = null
      this.loadContractOptions()
    },
  },
  async created() {
    this.loadSupplierOptions()
  },
  methods: {
    triggerFileInput() {
      this.$refs.fileInput.click()
    },
    getFileParams(params) {
      this.form.file = params.file
    },
    handleFileChange(event) {
      const file = event.target.files[0]
      if (file) {
        this.form.file = file
        this.$refs.form.validateField('file')
      }
    },
    async onSubmit() {
      const valid = await this.$refs.form.validate()
      if (!valid) return

      this.loading = true
      try {
        const formData = new FormData()
        formData.append('customerId', this.form.customerId)
        formData.append(
          'supplierCorporationId',
          this.form.supplierCorporationId
        )
        formData.append('contractId', this.form.contractId)
        formData.append('taxPeriod', this.form.taxPeriod)
        formData.append('file', this.form.file)

        const [err, response] = await client.supplierSalaryAddPayroll({
          body: formData
        })

        if (err) {
          handleError(err)
          return
        }

        // 检查响应数据中的错误情况
        if (response && response.success && response.data) {
          const { successCount, failCount, uuid } = response.data

          if (failCount === 0) {
            // 全部成功
            const actualSuccessCount = successCount || '所有'
            this.$message.success(`成功创建${actualSuccessCount}条数据`)
            this.$router.push('/payrolls')
          } else if (failCount > 0 && uuid) {
            // 有失败的数据，显示错误对话框
            this.errorUuid = uuid
            this.showErrorDialog = true
          } else {
            // 有失败但没有uuid
            this.$message.error(`创建失败，共${failCount}条数据有问题`)
          }
        } else {
          this.$message.success('创建成功')
          this.$router.push('/payrolls')
        }
      } finally {
        this.loading = false
      }
    },
    async downloadTemplate() {
      try {
        // 使用fetch API携带token下载文件
        const token = this.headerToken.Authorization
        const response = await fetch(`${window.env?.apiPath}/api/supplier/salary/download/template?taxCalculationMethod=PAYROLL_ADD`, {
          method: 'POST',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/octet-stream'
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 获取文件blob
        const blob = await response.blob()

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = '薪资导入模板.xlsx'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理URL对象
        window.URL.revokeObjectURL(url)
      } catch (error) {
        console.error('下载模板失败：', error)
        this.$message.error('下载模板失败')
      }
    },

    async exportErrorData() {
      try {
        // 使用fetch API携带token下载错误文件
        const token = this.headerToken.Authorization
        const response = await fetch(`${window.env?.apiPath}/api/supplier/salary/importVerifyErrorLog/${this.errorUuid}`, {
          method: 'POST',
          headers: {
            'Authorization': token,
            'Content-Type': 'application/octet-stream'
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 获取文件blob
        const blob = await response.blob()

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = '薪资导入错误数据.xls'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理URL对象
        window.URL.revokeObjectURL(url)
      } catch (error) {
        console.error('导出错误数据失败：', error)
        this.$message.error('导出错误数据失败')
      }
    },

    closeErrorDialog() {
      this.showErrorDialog = false
      this.errorUuid = ''
    },
    async loadContractOptions() {
      if (!this.form.customerId || !this.form.supplierCorporationId) {
        this.contractOptions = []
        return
      }
      const [err, response] = await client.listContractByCustomerAndCorporation({
        body: {
          filters: {
            customerId: this.form.customerId,
            supplierCorporationId: this.form.supplierCorporationId
          }
        }
      })
      if (err) {
        handleError(err)
        this.contractOptions = []
        return
      }
      this.contractOptions = response?.data?.list || []
    },
    async loadSupplierOptions() {
      try {
        const [err, response] = await client.listCorporation({
          body: { filters: {} }
        })
        if (response && response.success && response.data) {
          this.supplierOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载作业主体选项失败:', error)
      }
    },
  }
}
</script>